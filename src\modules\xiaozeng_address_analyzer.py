#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小曾收款地址分析器
通过链上查询获取小曾所有币安充值收款地址，实现交集分析和递归追踪
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Set, Tuple, Optional
import logging
from collections import defaultdict, Counter
import time

try:
    from .data_reader import ExcelDataReader
    from .address_validator import AddressValidator
    from .address_collector import AddressCollector
except ImportError:
    from data_reader import ExcelDataReader
    from address_validator import AddressValidator
    from address_collector import AddressCollector

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XiaozengAddressAnalyzer:
    """小曾收款地址分析器"""
    
    def __init__(self, data_reader: ExcelDataReader):
        """
        初始化分析器
        
        Args:
            data_reader: Excel数据读取器
        """
        self.data_reader = data_reader
        self.validator = AddressValidator()
        self.collector = AddressCollector(data_reader)
        
        # 分析参数
        self.time_tolerance_hours = 1  # 时间匹配容差：±1小时
        self.amount_tolerance_usdt = 3  # 金额匹配容差：<3 USDT
        self.min_sender_count = 3  # 最小发送地址数量：≥3个
        self.min_amount_threshold = 100  # 递归截止金额：<100 USDT
        
    def normalize_datetime(self, dt_value):
        """标准化时间值为datetime对象"""
        if pd.isna(dt_value):
            return None
        
        try:
            if isinstance(dt_value, datetime):
                return dt_value
            elif hasattr(dt_value, 'to_pydatetime'):
                return dt_value.to_pydatetime()
            elif isinstance(dt_value, (int, float)):
                # Excel日期序列号转换
                if 1 <= dt_value <= 73050:
                    base_date = datetime(1900, 1, 1)
                    return base_date + timedelta(days=dt_value - 2)
                else:
                    return None
            else:
                return None
        except (ValueError, OverflowError) as e:
            logger.warning(f"时间转换失败: {dt_value}, 错误: {e}")
            return None
    
    def extract_binance_deposit_info(self) -> List[Dict]:
        """
        从小曾币安充值记录中提取发送地址信息
        
        Returns:
            发送地址信息列表
        """
        logger.info("提取小曾币安充值记录...")
        
        # 读取小曾币安充值记录
        xz_deposits = self.data_reader.read_xiaozeng_binance_deposits()
        
        deposit_info = []
        for idx, record in xz_deposits.iterrows():
            sender_addr = record.get('发送地址')
            deposit_time = self.normalize_datetime(record.get('时间'))
            usdt_amount = record.get('USDT对应数额', 0)
            
            if pd.notna(sender_addr) and deposit_time:
                # 处理不同类型的地址数据
                if isinstance(sender_addr, str):
                    addr_str = sender_addr.strip()
                elif isinstance(sender_addr, (int, float)):
                    addr_str = str(sender_addr).strip()
                else:
                    continue
                
                if addr_str and addr_str != 'nan':
                    deposit_info.append({
                        'sender_address': addr_str,
                        'deposit_time': deposit_time,
                        'usdt_amount': float(usdt_amount) if pd.notna(usdt_amount) else 0,
                        'record_index': idx,
                        'original_record': record.to_dict()
                    })
        
        logger.info(f"提取到 {len(deposit_info)} 条币安充值记录")
        return deposit_info
    
    def query_sender_transactions(self, sender_info: Dict) -> List[Dict]:
        """
        查询发送地址的交易记录（带时间和金额过滤）
        
        Args:
            sender_info: 发送地址信息
            
        Returns:
            符合条件的交易记录列表
        """
        sender_addr = sender_info['sender_address']
        deposit_time = sender_info['deposit_time']
        deposit_amount = sender_info['usdt_amount']
        
        # 验证地址格式
        validation = self.validator.validate_address(sender_addr)
        if not validation['format_valid'] or validation['should_ignore']:
            logger.debug(f"跳过无效地址: {sender_addr}")
            return []
        
        # 计算时间范围
        start_time = deposit_time - timedelta(hours=self.time_tolerance_hours)
        end_time = deposit_time + timedelta(hours=self.time_tolerance_hours)
        
        logger.info(f"查询地址 {sender_addr} 的交易记录 (时间范围: {start_time} ~ {end_time})")
        
        # 根据地址类型查询交易
        transactions = []
        if validation['address_type'] == 'TRC20':
            transactions = self.collector.query_trc20_usdt_transactions_with_time_filter(
                sender_addr, start_time, end_time, limit=100
            )
        elif validation['address_type'] == 'ERC20':
            transactions = self.collector.query_erc20_usdt_transactions_with_time_filter(
                sender_addr, start_time, end_time, limit=100
            )
        
        # 过滤符合条件的交易
        filtered_transactions = []
        for tx in transactions:
            # 检查是否为发送交易
            if tx.get('from_address') != sender_addr:
                continue
            
            # 检查时间匹配
            tx_time = tx.get('timestamp')
            if not tx_time:
                continue
            
            time_diff = abs((tx_time - deposit_time).total_seconds()) / 3600  # 转换为小时
            if time_diff > self.time_tolerance_hours:
                continue
            
            # 检查金额匹配
            tx_amount = tx.get('amount_usdt', 0)
            amount_diff = abs(tx_amount - deposit_amount)
            if amount_diff >= self.amount_tolerance_usdt:
                continue
            
            # 添加匹配信息
            tx['deposit_info'] = sender_info
            tx['time_diff_hours'] = time_diff
            tx['amount_diff_usdt'] = amount_diff
            tx['matches_criteria'] = True
            
            filtered_transactions.append(tx)
        
        logger.info(f"地址 {sender_addr} 找到 {len(filtered_transactions)} 条符合条件的交易")
        return filtered_transactions
    
    def perform_intersection_analysis(self, all_transactions: List[Dict]) -> Dict:
        """
        执行交集分析，找出被多个发送地址转账的收款地址
        
        Args:
            all_transactions: 所有符合条件的交易记录
            
        Returns:
            交集分析结果
        """
        logger.info("执行收款地址交集分析...")
        
        # 统计每个收款地址被多少个不同发送地址转账
        receive_address_senders = defaultdict(set)
        receive_address_transactions = defaultdict(list)
        
        for tx in all_transactions:
            receive_addr = tx.get('to_address')
            sender_addr = tx.get('from_address')
            
            if receive_addr and sender_addr:
                receive_address_senders[receive_addr].add(sender_addr)
                receive_address_transactions[receive_addr].append(tx)
        
        # 筛选出被≥3个不同发送地址转账的收款地址
        xiaozeng_real_addresses = {}
        for receive_addr, senders in receive_address_senders.items():
            sender_count = len(senders)
            if sender_count >= self.min_sender_count:
                xiaozeng_real_addresses[receive_addr] = {
                    'sender_count': sender_count,
                    'senders': list(senders),
                    'transactions': receive_address_transactions[receive_addr],
                    'total_amount': sum(tx.get('amount_usdt', 0) for tx in receive_address_transactions[receive_addr]),
                    'transaction_count': len(receive_address_transactions[receive_addr])
                }
        
        # 生成统计信息
        all_receive_addresses = list(receive_address_senders.keys())
        sender_count_distribution = Counter(len(senders) for senders in receive_address_senders.values())
        
        result = {
            'xiaozeng_real_addresses': xiaozeng_real_addresses,
            'all_receive_addresses': all_receive_addresses,
            'statistics': {
                'total_receive_addresses': len(all_receive_addresses),
                'qualified_addresses': len(xiaozeng_real_addresses),
                'sender_count_distribution': dict(sender_count_distribution),
                'total_transactions': len(all_transactions)
            }
        }
        
        logger.info(f"交集分析完成: 发现 {len(xiaozeng_real_addresses)} 个小曾真实收款地址")
        return result
    
    def recursive_trace_transactions(self, address: str, visited: Set[str] = None, depth: int = 0, max_depth: int = 3) -> List[Dict]:
        """
        递归追踪地址的交易记录（金额≥100 USDT）
        
        Args:
            address: 要追踪的地址
            visited: 已访问的地址集合
            depth: 当前递归深度
            max_depth: 最大递归深度
            
        Returns:
            递归追踪到的交易记录
        """
        if visited is None:
            visited = set()
        
        if address in visited or depth > max_depth:
            return []
        
        visited.add(address)
        logger.info(f"递归追踪地址 {address} (深度: {depth})")
        
        # 验证地址
        validation = self.validator.validate_address(address)
        if not validation['format_valid'] or validation['should_ignore']:
            return []
        
        # 查询交易记录
        transactions = []
        if validation['address_type'] == 'TRC20':
            transactions = self.collector.query_trc20_usdt_transactions(address, limit=200)
        elif validation['address_type'] == 'ERC20':
            transactions = self.collector.query_erc20_usdt_transactions(address, limit=200)
        
        # 过滤和递归处理
        traced_transactions = []
        for tx in transactions:
            tx_amount = tx.get('amount_usdt', 0)
            
            # 只处理金额≥100 USDT的交易
            if tx_amount >= self.min_amount_threshold:
                tx['trace_depth'] = depth
                tx['trace_source'] = address
                traced_transactions.append(tx)
                
                # 如果当前地址是发送方，递归追踪接收方
                if tx.get('from_address') == address:
                    receive_addr = tx.get('to_address')
                    if receive_addr and receive_addr not in visited:
                        time.sleep(0.1)  # 避免API频率限制
                        sub_transactions = self.recursive_trace_transactions(
                            receive_addr, visited.copy(), depth + 1, max_depth
                        )
                        traced_transactions.extend(sub_transactions)
        
        return traced_transactions
    
    def analyze_xiaozeng_addresses(self) -> Dict:
        """
        完整分析小曾的收款地址
        
        Returns:
            完整的分析结果
        """
        logger.info("开始分析小曾的收款地址...")
        
        # 1. 提取币安充值记录
        deposit_info_list = self.extract_binance_deposit_info()
        
        # 2. 查询每个发送地址的交易记录
        all_filtered_transactions = []
        sender_analysis = {}
        
        for deposit_info in deposit_info_list:
            sender_addr = deposit_info['sender_address']
            
            try:
                transactions = self.query_sender_transactions(deposit_info)
                all_filtered_transactions.extend(transactions)
                
                sender_analysis[sender_addr] = {
                    'deposit_info': deposit_info,
                    'transaction_count': len(transactions),
                    'transactions': transactions
                }
                
                # API请求间隔
                time.sleep(0.2)
                
            except Exception as e:
                logger.error(f"查询发送地址 {sender_addr} 失败: {e}")
                sender_analysis[sender_addr] = {
                    'deposit_info': deposit_info,
                    'error': str(e),
                    'transaction_count': 0,
                    'transactions': []
                }
        
        # 3. 执行交集分析
        intersection_result = self.perform_intersection_analysis(all_filtered_transactions)
        
        # 4. 对真实收款地址进行递归追踪（可选）
        recursive_traces = {}
        for real_addr in intersection_result['xiaozeng_real_addresses'].keys():
            try:
                logger.info(f"递归追踪真实收款地址: {real_addr}")
                traces = self.recursive_trace_transactions(real_addr)
                recursive_traces[real_addr] = traces
                time.sleep(0.3)  # 增加延迟
            except Exception as e:
                logger.error(f"递归追踪地址 {real_addr} 失败: {e}")
                recursive_traces[real_addr] = []
        
        # 5. 汇总结果
        result = {
            'deposit_records': {
                'total_count': len(deposit_info_list),
                'records': deposit_info_list
            },
            'sender_analysis': sender_analysis,
            'filtered_transactions': {
                'total_count': len(all_filtered_transactions),
                'transactions': all_filtered_transactions
            },
            'intersection_analysis': intersection_result,
            'recursive_traces': recursive_traces,
            'summary': {
                'total_deposit_records': len(deposit_info_list),
                'valid_sender_addresses': len([s for s in sender_analysis.values() if s.get('transaction_count', 0) > 0]),
                'total_filtered_transactions': len(all_filtered_transactions),
                'xiaozeng_real_addresses_count': len(intersection_result['xiaozeng_real_addresses']),
                'recursive_trace_count': sum(len(traces) for traces in recursive_traces.values())
            }
        }
        
        logger.info("小曾收款地址分析完成")
        logger.info(f"总结: 币安充值记录 {result['summary']['total_deposit_records']} 条")
        logger.info(f"      符合条件交易 {result['summary']['total_filtered_transactions']} 条")
        logger.info(f"      真实收款地址 {result['summary']['xiaozeng_real_addresses_count']} 个")
        
        return result
    
    def save_analysis_results(self, analysis_result: Dict, output_dir: str = ".") -> Dict:
        """
        保存分析结果到文件
        
        Args:
            analysis_result: 分析结果
            output_dir: 输出目录
            
        Returns:
            保存的文件信息
        """
        from pathlib import Path
        import json
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        saved_files = {}
        
        try:
            # 1. 保存小曾真实收款地址
            real_addresses = list(analysis_result['intersection_analysis']['xiaozeng_real_addresses'].keys())
            real_addresses_file = output_path / 'xiaozeng_real_receive_addresses.txt'
            with open(real_addresses_file, 'w', encoding='utf-8') as f:
                f.write("# 小曾真实收款地址（交集分析结果）\n")
                f.write(f"# 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 匹配条件: 时间±{self.time_tolerance_hours}小时, 金额误差<{self.amount_tolerance_usdt}USDT, 最少{self.min_sender_count}个发送地址\n")
                f.write(f"# 总计: {len(real_addresses)} 个地址\n\n")
                for addr in real_addresses:
                    f.write(f"{addr}\n")
            saved_files['real_addresses'] = str(real_addresses_file)
            
            # 2. 保存收款地址统计分析
            stats_file = output_path / 'xiaozeng_address_statistics.txt'
            with open(stats_file, 'w', encoding='utf-8') as f:
                f.write("# 小曾收款地址统计分析\n")
                f.write(f"# 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 总体统计
                summary = analysis_result['summary']
                f.write("## 总体统计\n")
                f.write(f"币安充值记录总数: {summary['total_deposit_records']}\n")
                f.write(f"有效发送地址数量: {summary['valid_sender_addresses']}\n")
                f.write(f"符合条件交易数量: {summary['total_filtered_transactions']}\n")
                f.write(f"真实收款地址数量: {summary['xiaozeng_real_addresses_count']}\n")
                f.write(f"递归追踪交易数量: {summary['recursive_trace_count']}\n\n")
                
                # 收款地址详细统计
                f.write("## 收款地址详细统计\n")
                real_addresses_info = analysis_result['intersection_analysis']['xiaozeng_real_addresses']
                for addr, info in real_addresses_info.items():
                    f.write(f"\n地址: {addr}\n")
                    f.write(f"  发送地址数量: {info['sender_count']}\n")
                    f.write(f"  交易次数: {info['transaction_count']}\n")
                    f.write(f"  总金额: {info['total_amount']:.2f} USDT\n")
                    f.write(f"  发送地址列表: {', '.join(info['senders'])}\n")
                
                # 发送地址数量分布
                f.write("\n## 发送地址数量分布\n")
                distribution = analysis_result['intersection_analysis']['statistics']['sender_count_distribution']
                for count, freq in sorted(distribution.items()):
                    f.write(f"被 {count} 个发送地址转账的收款地址: {freq} 个\n")
            
            saved_files['statistics'] = str(stats_file)
            
            # 3. 保存符合条件的交易记录
            transactions_file = output_path / 'xiaozeng_filtered_transactions.csv'
            transactions_data = []
            for tx in analysis_result['filtered_transactions']['transactions']:
                transactions_data.append({
                    '交易哈希': tx.get('tx_hash', ''),
                    '发送地址': tx.get('from_address', ''),
                    '接收地址': tx.get('to_address', ''),
                    '金额USDT': tx.get('amount_usdt', 0),
                    '交易时间': tx.get('timestamp', ''),
                    '币安充值时间': tx.get('deposit_info', {}).get('deposit_time', ''),
                    '时间差小时': tx.get('time_diff_hours', 0),
                    '金额差USDT': tx.get('amount_diff_usdt', 0),
                    '区块链类型': tx.get('api_source', ''),
                    '区块号': tx.get('block', '')
                })
            
            if transactions_data:
                df = pd.DataFrame(transactions_data)
                df.to_csv(transactions_file, index=False, encoding='utf-8-sig')
                saved_files['transactions'] = str(transactions_file)
            
            # 4. 保存完整分析结果（JSON格式）
            json_file = output_path / 'xiaozeng_analysis_complete.json'
            # 处理datetime对象，使其可以JSON序列化
            def json_serializer(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=json_serializer)
            saved_files['complete_json'] = str(json_file)
            
            logger.info(f"分析结果已保存到 {len(saved_files)} 个文件")
            return saved_files
            
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return {'error': str(e)}

def main():
    """测试函数"""
    from data_reader import ExcelDataReader
    
    # 初始化
    reader = ExcelDataReader("../..")
    analyzer = XiaozengAddressAnalyzer(reader)
    
    # 执行分析
    result = analyzer.analyze_xiaozeng_addresses()
    
    # 保存结果
    saved_files = analyzer.save_analysis_results(result)
    
    print("=" * 60)
    print("小曾收款地址分析完成")
    print("=" * 60)
    print(f"币安充值记录: {result['summary']['total_deposit_records']} 条")
    print(f"符合条件交易: {result['summary']['total_filtered_transactions']} 条")
    print(f"真实收款地址: {result['summary']['xiaozeng_real_addresses_count']} 个")
    print(f"递归追踪交易: {result['summary']['recursive_trace_count']} 条")
    print("\n保存的文件:")
    for file_type, file_path in saved_files.items():
        print(f"  {file_type}: {file_path}")

if __name__ == "__main__":
    main()