#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终端界面 - Rich + 交互式命令行
提供美观的终端用户界面
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from datetime import datetime

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.prompt import Prompt, Confirm
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.layout import Layout
    from rich.text import Text
    from rich.columns import Columns
    from rich import box
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

class TerminalInterface:
    """终端界面类"""
    
    def __init__(self):
        """初始化界面"""
        if RICH_AVAILABLE:
            self.console = Console()
        else:
            self.console = None
            
        # 初始化工具
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
    
    def print_header(self):
        """打印标题"""
        if self.console:
            title = Text("🔍 MoneyTrace 地址测试工具", style="bold blue")
            subtitle = Text("测试区块链地址有效性和查询交易记录", style="dim")
            
            panel = Panel.fit(
                f"{title}\n{subtitle}",
                border_style="blue",
                padding=(1, 2)
            )
            self.console.print(panel)
        else:
            print("=" * 60)
            print("🔍 MoneyTrace 地址测试工具")
            print("测试区块链地址有效性和查询交易记录")
            print("=" * 60)
    
    def show_menu(self):
        """显示主菜单"""
        if self.console:
            menu_items = [
                "[1] 单地址测试",
                "[2] 批量地址测试", 
                "[3] 交易记录查询",
                "[4] 快速示例测试",
                "[5] 退出程序"
            ]
            
            menu_panel = Panel(
                "\n".join(menu_items),
                title="📋 主菜单",
                border_style="green",
                padding=(1, 2)
            )
            self.console.print(menu_panel)
        else:
            print("\n📋 主菜单:")
            print("1. 单地址测试")
            print("2. 批量地址测试")
            print("3. 交易记录查询")
            print("4. 快速示例测试")
            print("5. 退出程序")
    
    def single_address_test(self):
        """单地址测试"""
        if self.console:
            self.console.print("\n[bold cyan]单地址测试[/bold cyan]")
            address = Prompt.ask("请输入地址")
        else:
            print("\n单地址测试")
            address = input("请输入地址: ").strip()
        
        if not address:
            return
        
        # 显示进度
        if self.console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("验证地址中...", total=None)
                result = self.validator.validate_address(address)
                progress.update(task, description="验证完成")
        else:
            print("验证中...")
            result = self.validator.validate_address(address)
        
        # 显示结果
        self.display_single_result(address, result)
    
    def display_single_result(self, address, result):
        """显示单地址结果"""
        if self.console:
            # 创建结果表格
            table = Table(title="验证结果", box=box.ROUNDED)
            table.add_column("项目", style="cyan", no_wrap=True)
            table.add_column("结果", style="magenta")
            
            table.add_row("地址", address)
            table.add_row("类型", result['address_type'])
            table.add_row("格式有效", "✅ 是" if result['format_valid'] else "❌ 否")
            table.add_row("链上存在", "✅ 是" if result['exists_on_chain'] else "❌ 否")
            table.add_row("应忽略", "⚠️ 是" if result['should_ignore'] else "✅ 否")
            
            if result['errors']:
                table.add_row("错误信息", "; ".join(result['errors']))
            
            self.console.print(table)
            
            # 状态面板
            if result['format_valid'] and result['exists_on_chain'] and not result['should_ignore']:
                status_panel = Panel("✅ 地址有效，可用于进一步分析", 
                                   title="状态", border_style="green")
            elif result['should_ignore']:
                status_panel = Panel("⚠️ 地址应忽略", 
                                   title="状态", border_style="yellow")
            else:
                status_panel = Panel("❌ 地址无效", 
                                   title="状态", border_style="red")
            
            self.console.print(status_panel)
        else:
            print(f"\n验证结果:")
            print(f"地址: {address}")
            print(f"类型: {result['address_type']}")
            print(f"格式有效: {'✅ 是' if result['format_valid'] else '❌ 否'}")
            print(f"链上存在: {'✅ 是' if result['exists_on_chain'] else '❌ 否'}")
            print(f"应忽略: {'⚠️ 是' if result['should_ignore'] else '✅ 否'}")
            
            if result['errors']:
                print(f"错误信息: {'; '.join(result['errors'])}")
    
    def batch_address_test(self):
        """批量地址测试"""
        if self.console:
            self.console.print("\n[bold cyan]批量地址测试[/bold cyan]")
            self.console.print("请输入地址列表（每行一个地址，输入空行结束）:")
        else:
            print("\n批量地址测试")
            print("请输入地址列表（每行一个地址，输入空行结束）:")
        
        addresses = []
        while True:
            if self.console:
                addr = Prompt.ask(f"地址 {len(addresses) + 1}", default="")
            else:
                addr = input(f"地址 {len(addresses) + 1}: ").strip()
            
            if not addr:
                break
            addresses.append(addr)
        
        if not addresses:
            return
        
        # 批量验证
        if self.console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task(f"验证 {len(addresses)} 个地址...", total=None)
                results = self.validator.validate_address_list(addresses)
                progress.update(task, description="验证完成")
        else:
            print(f"验证 {len(addresses)} 个地址...")
            results = self.validator.validate_address_list(addresses)
        
        # 显示结果
        self.display_batch_results(results)
    
    def display_batch_results(self, results):
        """显示批量结果"""
        if self.console:
            # 统计表格
            stats_table = Table(title="批量验证统计", box=box.ROUNDED)
            stats_table.add_column("统计项", style="cyan")
            stats_table.add_column("数量", style="magenta", justify="right")
            
            stats_table.add_row("总地址数", str(results['total_addresses']))
            stats_table.add_row("有效地址", str(len(results['valid_addresses'])))
            stats_table.add_row("无效地址", str(len(results['invalid_addresses'])))
            stats_table.add_row("忽略地址", str(len(results['ignored_addresses'])))
            stats_table.add_row("TRC20地址", str(len(results['trc20_addresses'])))
            stats_table.add_row("ERC20地址", str(len(results['erc20_addresses'])))
            
            self.console.print(stats_table)
            
            # 有效地址列表
            if results['valid_addresses']:
                valid_panel = Panel(
                    "\n".join(f"• {addr}" for addr in results['valid_addresses']),
                    title="✅ 有效地址",
                    border_style="green"
                )
                self.console.print(valid_panel)
            
            # 无效地址列表（只显示前10个）
            if results['invalid_addresses']:
                invalid_list = results['invalid_addresses'][:10]
                if len(results['invalid_addresses']) > 10:
                    invalid_list.append(f"... 还有 {len(results['invalid_addresses']) - 10} 个")
                
                invalid_panel = Panel(
                    "\n".join(f"• {addr}" for addr in invalid_list),
                    title="❌ 无效地址",
                    border_style="red"
                )
                self.console.print(invalid_panel)
        else:
            print(f"\n批量验证结果:")
            print(f"总地址数: {results['total_addresses']}")
            print(f"有效地址: {len(results['valid_addresses'])}")
            print(f"无效地址: {len(results['invalid_addresses'])}")
            print(f"忽略地址: {len(results['ignored_addresses'])}")
            print(f"TRC20地址: {len(results['trc20_addresses'])}")
            print(f"ERC20地址: {len(results['erc20_addresses'])}")
            
            if results['valid_addresses']:
                print(f"\n✅ 有效地址:")
                for addr in results['valid_addresses']:
                    print(f"  - {addr}")
    
    def transaction_query(self):
        """交易查询"""
        if self.console:
            self.console.print("\n[bold cyan]交易记录查询[/bold cyan]")
            address = Prompt.ask("请输入地址")
        else:
            print("\n交易记录查询")
            address = input("请输入地址: ").strip()
        
        if not address:
            return
        
        # 查询交易
        if self.console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("查询交易中...", total=None)
                
                # 先验证地址
                validation = self.validator.validate_address(address)
                progress.update(task, description="验证地址完成，查询交易...")
                
                if validation['format_valid'] and not validation['should_ignore']:
                    if validation['address_type'] == 'TRC20':
                        transactions = self.collector.query_trc20_transaction(address, None)
                    elif validation['address_type'] == 'ERC20':
                        transactions = self.collector.query_erc20_transaction(address, None)
                    else:
                        transactions = []
                else:
                    transactions = []
                
                progress.update(task, description="查询完成")
        else:
            print("查询交易中...")
            validation = self.validator.validate_address(address)
            
            if validation['format_valid'] and not validation['should_ignore']:
                if validation['address_type'] == 'TRC20':
                    transactions = self.collector.query_trc20_transaction(address, None)
                elif validation['address_type'] == 'ERC20':
                    transactions = self.collector.query_erc20_transaction(address, None)
                else:
                    transactions = []
            else:
                transactions = []
        
        # 显示结果
        self.display_transaction_results(address, validation, transactions)
    
    def display_transaction_results(self, address, validation, transactions):
        """显示交易结果"""
        if self.console:
            # 基本信息
            info_table = Table(title="地址信息", box=box.ROUNDED)
            info_table.add_column("项目", style="cyan")
            info_table.add_column("值", style="magenta")
            
            info_table.add_row("地址", address)
            info_table.add_row("类型", validation['address_type'])
            info_table.add_row("交易数量", str(len(transactions)))
            
            self.console.print(info_table)
            
            # 交易列表
            if transactions:
                tx_table = Table(title="交易记录（前10条）", box=box.ROUNDED)
                tx_table.add_column("序号", style="cyan", width=4)
                tx_table.add_column("交易哈希", style="blue", width=20)
                tx_table.add_column("发送方", style="green", width=15)
                tx_table.add_column("接收方", style="yellow", width=15)
                tx_table.add_column("金额", style="magenta", width=12)
                tx_table.add_column("时间", style="white", width=20)
                
                for i, tx in enumerate(transactions[:10], 1):
                    tx_table.add_row(
                        str(i),
                        tx.get('tx_hash', 'N/A')[:18] + "...",
                        tx.get('from_address', 'N/A')[:13] + "...",
                        tx.get('to_address', 'N/A')[:13] + "...",
                        str(tx.get('amount', 'N/A')),
                        str(tx.get('timestamp', 'N/A'))
                    )
                
                self.console.print(tx_table)
                
                if len(transactions) > 10:
                    self.console.print(f"[dim]... 还有 {len(transactions) - 10} 条交易[/dim]")
            else:
                no_tx_panel = Panel("❌ 未找到交易记录", border_style="red")
                self.console.print(no_tx_panel)
        else:
            print(f"\n交易查询结果:")
            print(f"地址: {address}")
            print(f"类型: {validation['address_type']}")
            print(f"交易数量: {len(transactions)}")
            
            if transactions:
                print(f"\n交易记录（前10条）:")
                for i, tx in enumerate(transactions[:10], 1):
                    print(f"  交易 {i}:")
                    print(f"    哈希: {tx.get('tx_hash', 'N/A')}")
                    print(f"    发送方: {tx.get('from_address', 'N/A')}")
                    print(f"    接收方: {tx.get('to_address', 'N/A')}")
                    print(f"    金额: {tx.get('amount', 'N/A')}")
                    print(f"    时间: {tx.get('timestamp', 'N/A')}")
                
                if len(transactions) > 10:
                    print(f"  ... 还有 {len(transactions) - 10} 条交易")
            else:
                print("❌ 未找到交易记录")
    
    def quick_sample_test(self):
        """快速示例测试"""
        sample_addresses = [
            "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew",  # 小江TRC20地址
            "0x61f73c531c47d345ee642566d9d9fb38db287cdb",  # 小江ERC20地址
            "TXVL9P8ZiiABWq6D66HndrtVC2bQegve9q",  # 数据中的地址
            "135343225",  # 小江UID（应忽略）
            "invalid_test_address",  # 无效地址
        ]
        
        if self.console:
            self.console.print("\n[bold cyan]快速示例测试[/bold cyan]")
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("测试示例地址...", total=None)
                results = self.validator.validate_address_list(sample_addresses)
                progress.update(task, description="测试完成")
        else:
            print("\n快速示例测试")
            print("测试示例地址...")
            results = self.validator.validate_address_list(sample_addresses)
        
        self.display_batch_results(results)
    
    def run(self):
        """运行主程序"""
        self.print_header()
        
        while True:
            self.show_menu()
            
            if self.console:
                choice = Prompt.ask("请选择操作", choices=["1", "2", "3", "4", "5"], default="1")
            else:
                choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                self.single_address_test()
            elif choice == '2':
                self.batch_address_test()
            elif choice == '3':
                self.transaction_query()
            elif choice == '4':
                self.quick_sample_test()
            elif choice == '5':
                if self.console:
                    self.console.print("[bold green]感谢使用！[/bold green]")
                else:
                    print("感谢使用！")
                break
            else:
                if self.console:
                    self.console.print("[bold red]无效选项，请重新选择[/bold red]")
                else:
                    print("无效选项，请重新选择")

def main():
    """主函数"""
    if not RICH_AVAILABLE:
        print("警告: 未安装 rich 库，将使用简化界面")
        print("安装命令: pip install rich")
        print()
    
    interface = TerminalInterface()
    interface.run()

if __name__ == "__main__":
    main()
