#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据读取模块
支持读取项目中的各种.xls格式文件并转换为pandas DataFrame
"""

import xlrd
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExcelDataReader:
    """Excel数据读取器"""
    
    def __init__(self, data_dir: str = "."):
        """
        初始化数据读取器
        
        Args:
            data_dir: 数据文件目录路径
        """
        self.data_dir = Path(data_dir)
        self.file_configs = {
            # 小曾相关文件
            'xzbankin.xls': {
                'description': '小曾银行流水',
                'sheet_index': 0,
                'expected_columns': ['交易卡号', '反馈结果', '交易金额', '收付标志', '交易时间', '交易对手账卡号', '对手户名', '摘要说明', '交易币种']
            },
            'xzbain.xls': {
                'description': '小曾币安充值记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            },
            'xzbasale.xls': {
                'description': '小曾P2P卖出记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            },
            'xzwxin.xls': {
                'description': '小曾微信收款记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            },
            # 小江相关文件
            'xjbain.xls': {
                'description': '小江币安充值记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            },
            'xjbankinout.xls': {
                'description': '小江银行出入金记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            },
            'xjbasale.xls': {
                'description': '小江P2P卖出记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            },
            'xjwxin.xls': {
                'description': '小江微信收款记录',
                'sheet_index': 0,
                'expected_columns': None  # 待确定
            }
        }
    
    def read_xls_file(self, filename: str, sheet_index: int = 0) -> pd.DataFrame:
        """
        读取单个.xls文件
        
        Args:
            filename: 文件名
            sheet_index: 工作表索引
            
        Returns:
            pandas DataFrame
        """
        file_path = self.data_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            # 使用xlrd读取.xls文件
            workbook = xlrd.open_workbook(file_path)
            sheet = workbook.sheet_by_index(sheet_index)
            
            # 提取数据
            data = []
            headers = []
            
            # 读取表头（第一行）
            if sheet.nrows > 0:
                headers = [sheet.cell_value(0, col) for col in range(sheet.ncols)]
            
            # 读取数据行
            for row_idx in range(1, sheet.nrows):
                row_data = []
                for col_idx in range(sheet.ncols):
                    cell_value = sheet.cell_value(row_idx, col_idx)
                    
                    # 处理Excel日期格式
                    if sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_DATE:
                        cell_value = xlrd.xldate_as_datetime(cell_value, workbook.datemode)
                    
                    row_data.append(cell_value)
                data.append(row_data)
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=headers)
            
            logger.info(f"成功读取文件 {filename}: {df.shape[0]} 行, {df.shape[1]} 列")
            return df
            
        except Exception as e:
            logger.error(f"读取文件 {filename} 失败: {e}")
            raise
    
    def read_xiaozeng_binance_deposits(self) -> pd.DataFrame:
        """读取小曾币安充值记录"""
        return self.read_xls_file('xzbain.xls')
    
    def read_xiaozeng_bank_records(self) -> pd.DataFrame:
        """读取小曾银行流水记录"""
        return self.read_xls_file('xzbankin.xls')
    
    def read_xiaozeng_p2p_sales(self) -> pd.DataFrame:
        """读取小曾P2P卖出记录"""
        return self.read_xls_file('xzbasale.xls')
    
    def read_xiaozeng_wechat_receipts(self) -> pd.DataFrame:
        """读取小曾微信收款记录"""
        return self.read_xls_file('xzwxin.xls')
    
    def read_xiaojiang_binance_deposits(self) -> pd.DataFrame:
        """读取小江币安充值记录"""
        return self.read_xls_file('xjbain.xls')
    
    def read_xiaojiang_bank_records(self) -> pd.DataFrame:
        """读取小江银行出入金记录"""
        return self.read_xls_file('xjbankinout.xls')
    
    def read_xiaojiang_p2p_sales(self) -> pd.DataFrame:
        """读取小江P2P卖出记录"""
        return self.read_xls_file('xjbasale.xls')
    
    def read_xiaojiang_wechat_receipts(self) -> pd.DataFrame:
        """读取小江微信收款记录"""
        return self.read_xls_file('xjwxin.xls')
    
    def get_file_info(self, filename: str) -> Dict:
        """
        获取文件基本信息
        
        Args:
            filename: 文件名
            
        Returns:
            文件信息字典
        """
        file_path = self.data_dir / filename
        
        if not file_path.exists():
            return {'exists': False, 'error': f'文件不存在: {filename}'}
        
        try:
            workbook = xlrd.open_workbook(file_path)
            sheet = workbook.sheet_by_index(0)
            
            # 获取表头
            headers = []
            if sheet.nrows > 0:
                headers = [sheet.cell_value(0, col) for col in range(sheet.ncols)]
            
            info = {
                'exists': True,
                'filename': filename,
                'description': self.file_configs.get(filename, {}).get('description', '未知'),
                'file_size': file_path.stat().st_size,
                'sheets_count': workbook.nsheets,
                'rows': sheet.nrows,
                'columns': sheet.ncols,
                'headers': headers,
                'sample_data': []
            }
            
            # 获取前3行样本数据
            for row_idx in range(min(3, sheet.nrows)):
                row_data = [sheet.cell_value(row_idx, col) for col in range(sheet.ncols)]
                info['sample_data'].append(row_data)
            
            return info
            
        except Exception as e:
            return {'exists': True, 'error': f'读取文件信息失败: {e}'}
    
    def analyze_all_files(self) -> Dict:
        """
        分析所有Excel文件的结构
        
        Returns:
            所有文件的分析结果
        """
        results = {}
        
        for filename in self.file_configs.keys():
            logger.info(f"分析文件: {filename}")
            results[filename] = self.get_file_info(filename)
        
        return results

def main():
    """测试函数"""
    reader = ExcelDataReader()
    
    print("=" * 60)
    print("Excel文件结构分析")
    print("=" * 60)
    
    # 分析所有文件
    results = reader.analyze_all_files()
    
    for filename, info in results.items():
        print(f"\n文件: {filename}")
        print(f"描述: {info.get('description', '未知')}")
        
        if info.get('exists') and 'error' not in info:
            print(f"大小: {info['file_size']} 字节")
            print(f"工作表数量: {info['sheets_count']}")
            print(f"数据形状: {info['rows']} 行 x {info['columns']} 列")
            print(f"列名: {info['headers']}")
            print("样本数据:")
            for i, row in enumerate(info['sample_data']):
                print(f"  行{i}: {row}")
        else:
            print(f"错误: {info.get('error', '未知错误')}")
        
        print("-" * 40)

if __name__ == "__main__":
    main()
