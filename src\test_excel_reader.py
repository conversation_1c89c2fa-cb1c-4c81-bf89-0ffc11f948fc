#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件读取测试脚本
用于测试不同的Excel读取方案并选择最佳技术栈
"""

import os
import sys
import time
from pathlib import Path

def test_pandas_openpyxl():
    """测试pandas + openpyxl方案"""
    try:
        import pandas as pd
        print("✓ pandas已安装")
        
        # 测试读取xzbankin.xls
        file_path = Path("../xzbankin.xls")
        if file_path.exists():
            start_time = time.time()
            df = pd.read_excel(file_path, engine='openpyxl')
            end_time = time.time()
            
            print(f"✓ pandas读取成功")
            print(f"  - 文件大小: {file_path.stat().st_size / 1024:.2f} KB")
            print(f"  - 读取时间: {end_time - start_time:.2f} 秒")
            print(f"  - 数据形状: {df.shape}")
            print(f"  - 列名: {list(df.columns)}")
            print(f"  - 前3行数据:")
            print(df.head(3))
            return True, df
        else:
            print("✗ 测试文件不存在")
            return False, None
            
    except ImportError as e:
        print(f"✗ pandas未安装: {e}")
        return False, None
    except Exception as e:
        print(f"✗ pandas读取失败: {e}")
        return False, None

def test_xlrd():
    """测试xlrd方案"""
    try:
        import xlrd
        print("✓ xlrd已安装")
        
        file_path = Path("../xzbankin.xls")
        if file_path.exists():
            start_time = time.time()
            workbook = xlrd.open_workbook(file_path)
            sheet = workbook.sheet_by_index(0)
            end_time = time.time()
            
            print(f"✓ xlrd读取成功")
            print(f"  - 读取时间: {end_time - start_time:.2f} 秒")
            print(f"  - 工作表数量: {workbook.nsheets}")
            print(f"  - 数据形状: {sheet.nrows} x {sheet.ncols}")
            
            # 显示前几行数据
            print(f"  - 前3行数据:")
            for row_idx in range(min(3, sheet.nrows)):
                row_data = [sheet.cell_value(row_idx, col_idx) for col_idx in range(sheet.ncols)]
                print(f"    {row_data}")
            return True, sheet
        else:
            print("✗ 测试文件不存在")
            return False, None
            
    except ImportError as e:
        print(f"✗ xlrd未安装: {e}")
        return False, None
    except Exception as e:
        print(f"✗ xlrd读取失败: {e}")
        return False, None

def test_openpyxl_only():
    """测试纯openpyxl方案"""
    try:
        import openpyxl
        print("✓ openpyxl已安装")
        
        file_path = Path("../xzbankin.xls")
        if file_path.exists():
            start_time = time.time()
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook.active
            end_time = time.time()
            
            print(f"✓ openpyxl读取成功")
            print(f"  - 读取时间: {end_time - start_time:.2f} 秒")
            print(f"  - 工作表数量: {len(workbook.sheetnames)}")
            print(f"  - 数据形状: {sheet.max_row} x {sheet.max_column}")
            
            # 显示前几行数据
            print(f"  - 前3行数据:")
            for row_idx in range(1, min(4, sheet.max_row + 1)):
                row_data = [sheet.cell(row_idx, col_idx).value for col_idx in range(1, sheet.max_column + 1)]
                print(f"    {row_data}")
            return True, sheet
        else:
            print("✗ 测试文件不存在")
            return False, None
            
    except ImportError as e:
        print(f"✗ openpyxl未安装: {e}")
        return False, None
    except Exception as e:
        print(f"✗ openpyxl读取失败: {e}")
        return False, None

def main():
    """主测试函数"""
    print("=" * 60)
    print("Excel文件读取技术方案测试")
    print("=" * 60)
    
    # 切换到正确的工作目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("\n1. 测试pandas + openpyxl方案:")
    print("-" * 40)
    pandas_success, pandas_data = test_pandas_openpyxl()
    
    print("\n2. 测试xlrd方案:")
    print("-" * 40)
    xlrd_success, xlrd_data = test_xlrd()
    
    print("\n3. 测试纯openpyxl方案:")
    print("-" * 40)
    openpyxl_success, openpyxl_data = test_openpyxl_only()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    if pandas_success:
        print("✓ 推荐方案: pandas + openpyxl")
        print("  - 优势: 数据分析功能强大，代码简洁")
        print("  - 适用场景: 需要复杂数据处理和分析")
    elif openpyxl_success:
        print("✓ 备选方案: 纯openpyxl")
        print("  - 优势: 专门处理Excel文件，功能完整")
        print("  - 适用场景: 主要进行Excel文件操作")
    elif xlrd_success:
        print("✓ 轻量方案: xlrd")
        print("  - 优势: 轻量级，内存占用小")
        print("  - 限制: 功能相对简单")
    else:
        print("✗ 所有方案都失败，需要安装相关依赖包")
        print("建议运行: pip install pandas openpyxl xlrd")

if __name__ == "__main__":
    main()
