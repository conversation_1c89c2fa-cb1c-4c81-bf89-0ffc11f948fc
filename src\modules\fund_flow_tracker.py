#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向追踪模块
根据multitrace.md需求实现多层级资金流向追踪功能

需求分析：
1. 从小江的第一个地址 TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew 开始
2. 获取该地址转出的所有USDT交易，先取第一条
3. 记录交易hash、金额m、时间t、对方地址b
4. 对地址b查询t后2个月内转出的USDT交易
5. 只记录金额在 m/4 < amount <= m+1 范围内的交易
6. 递归追踪直到：没有新地址 OR 地址链接数>4 OR 转移金额<m/8
7. 所有参数可配置：2个月、地址链接数4、1/8金额等
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Set
import logging
import time
from dataclasses import dataclass

try:
    from .address_validator import AddressValidator
    from .address_collector import AddressCollector
    from .data_reader import ExcelDataReader
except ImportError:
    from address_validator import AddressValidator
    from address_collector import AddressCollector
    from data_reader import ExcelDataReader

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TransactionRecord:
    """交易记录数据类"""
    hash: str
    from_address: str
    to_address: str
    amount: float
    timestamp: datetime
    balance: float = 0.0
    level: int = 0  # 追踪层级
    parent_hash: str = ""  # 父交易hash

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'hash': self.hash,
            'from_address': self.from_address,
            'to_address': self.to_address,
            'amount': self.amount,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'balance': self.balance,
            'level': self.level,
            'parent_hash': self.parent_hash
        }

@dataclass
class TrackingConfig:
    """追踪配置参数"""
    time_window_months: int = 2  # 时间窗口（月）
    max_levels: int = 4  # 最大地址链接数
    min_amount_ratio: float = 0.125  # 最小金额比例（1/8）
    max_amount_ratio: float = 1.0  # 最大金额比例（m+1倍）
    min_amount_filter_ratio: float = 0.25  # 最小过滤金额比例（1/4）
    api_delay: float = 2.0  # API调用延迟（秒）
    max_transactions_per_level: int = 10  # 每层最大交易数

class FundFlowTracker:
    """资金流向追踪器"""

    def __init__(self, config: Optional[TrackingConfig] = None):
        """
        初始化追踪器

        Args:
            config: 追踪配置参数
        """
        self.config = config or TrackingConfig()
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
        self.tracked_addresses: Set[str] = set()
        self.all_transactions: List[TransactionRecord] = []

    def track_fund_flow(self, start_address: str) -> Dict:
        """
        追踪资金流向 - 基于multitrace.md需求实现

        Args:
            start_address: 起始地址

        Returns:
            追踪结果字典
        """
        print(f"\n{'='*80}")
        print(f"MoneyTrace 资金流向追踪")
        print(f"{'='*80}")
        print(f"起始地址: {start_address}")
        print(f"配置参数:")
        print(f"  时间窗口: {self.config.time_window_months} 月")
        print(f"  最大层级: {self.config.max_levels}")
        print(f"  最小金额比例: {self.config.min_amount_ratio} (1/{1/self.config.min_amount_ratio:.0f})")
        print(f"  金额过滤比例: {self.config.min_amount_filter_ratio} (1/{1/self.config.min_amount_filter_ratio:.0f})")
        print(f"  API延迟: {self.config.api_delay} 秒")
        print(f"{'='*80}")

        # 重置状态
        self.tracked_addresses.clear()
        self.all_transactions.clear()

        # 验证起始地址
        print("🔍 步骤1: 验证起始地址")
        print("-" * 40)
        validation_result = self.validator.validate_address(start_address)

        print(f"地址: {start_address}")
        print(f"类型: {validation_result['address_type']}")
        print(f"格式有效: {'✅ 是' if validation_result['format_valid'] else '❌ 否'}")
        print(f"链上存在: {'✅ 是' if validation_result['exists_on_chain'] else '❌ 否'}")
        print(f"应忽略: {'⚠️ 是' if validation_result['should_ignore'] else '✅ 否'}")

        if not validation_result['format_valid']:
            return {
                'success': False,
                'error': f'起始地址 {start_address} 格式无效',
                'transactions': [],
                'summary': {},
                'validation': validation_result
            }

        if validation_result['should_ignore']:
            return {
                'success': False,
                'error': f'起始地址 {start_address} 在忽略列表中',
                'transactions': [],
                'summary': {},
                'validation': validation_result
            }

        try:
            print(f"\n🚀 步骤2: 开始递归追踪")
            print("-" * 40)

            # 开始递归追踪
            self._track_address_recursive(start_address, level=0, parent_amount=None, parent_hash="", parent_time=None)

            # 生成追踪结果
            result = self._generate_tracking_result(start_address)

            print(f"\n✅ 追踪完成！")
            print(f"共发现 {len(self.all_transactions)} 笔交易，涉及 {len(self.tracked_addresses)} 个地址")
            return result

        except Exception as e:
            logger.error(f"追踪过程中发生错误: {e}")
            print(f"\n❌ 追踪失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'transactions': [tx.to_dict() for tx in self.all_transactions],
                'summary': {},
                'partial_results': True,
                'validation': validation_result
            }

    def _track_address_recursive(self, address: str, level: int,
                                parent_amount: Optional[float], parent_hash: str, parent_time: Optional[datetime]):
        """
        递归追踪地址 - 核心追踪逻辑

        Args:
            address: 当前地址
            level: 当前层级
            parent_amount: 父交易金额
            parent_hash: 父交易hash
            parent_time: 父交易时间
        """
        # 检查终止条件
        if level > self.config.max_levels:
            print(f"    ⏹️ 达到最大层级 {self.config.max_levels}，停止追踪地址 {address[:16]}...")
            return

        if address in self.tracked_addresses:
            print(f"    🔄 地址 {address[:16]}... 已追踪过，跳过")
            return

        # 标记地址已追踪
        self.tracked_addresses.add(address)

        print(f"\n  📍 第 {level} 层 - 追踪地址: {address}")

        # 获取地址的USDT转出交易
        transactions = self._get_outgoing_usdt_transactions(address, parent_time)

        if not transactions:
            print(f"    ❌ 地址 {address[:16]}... 没有USDT转出交易")
            return

        print(f"    📋 找到 {len(transactions)} 条USDT转出交易")

        # 如果是第一层，只取第一条交易（按照multitrace.md要求）
        if level == 0:
            transactions = transactions[:1]
            print(f"    🎯 第一层地址，只处理第一条交易")
        else:
            # 其他层级限制交易数量
            transactions = transactions[:self.config.max_transactions_per_level]
            if len(transactions) > self.config.max_transactions_per_level:
                print(f"    ⚡ 限制处理前 {self.config.max_transactions_per_level} 条交易")

        processed_count = 0
        for i, tx_data in enumerate(transactions, 1):
            # 转换为TransactionRecord对象
            tx = self._convert_to_transaction_record(tx_data, level, parent_hash)

            # 检查金额过滤条件
            if parent_amount is not None:
                if not self._check_amount_filter(tx.amount, parent_amount):
                    print(f"    ⏭️ 交易{i}: 金额 {tx.amount:.6f} 不符合过滤条件，跳过")
                    continue

                # 检查最小金额终止条件
                min_threshold = parent_amount * self.config.min_amount_ratio
                if tx.amount < min_threshold:
                    print(f"    🛑 交易{i}: 金额 {tx.amount:.6f} < {min_threshold:.6f}，达到最小比例，停止追踪")
                    continue

            # 记录交易
            self.all_transactions.append(tx)
            processed_count += 1

            print(f"    ✅ 交易{i}: {tx.hash[:16]}... "
                  f"金额: {tx.amount:.6f} USDT "
                  f"到: {tx.to_address[:16]}...")

            # API调用延迟
            if self.config.api_delay > 0:
                print(f"    ⏳ 等待 {self.config.api_delay} 秒...")
                time.sleep(self.config.api_delay)

            # 递归追踪下一层
            self._track_address_recursive(
                tx.to_address,
                level + 1,
                tx.amount,
                tx.hash,
                tx.timestamp
            )

        print(f"    📊 第 {level} 层处理完成: 处理了 {processed_count}/{len(transactions)} 条交易")

    def _get_outgoing_usdt_transactions(self, address: str, parent_time: Optional[datetime]) -> List[Dict]:
        """
        获取地址的USDT转出交易 - 使用现有的collector方法

        Args:
            address: 地址
            parent_time: 父交易时间（用于时间窗口过滤）

        Returns:
            交易记录列表
        """
        try:
            # 验证地址类型
            validation = self.validator.validate_address(address)
            if not validation['format_valid']:
                return []

            transactions = []

            # 根据地址类型查询USDT交易
            if validation['address_type'] == 'TRC20':
                if parent_time:
                    # 使用时间窗口查询
                    end_time = parent_time + timedelta(days=30 * self.config.time_window_months)
                    transactions = self.collector.query_trc20_usdt_transactions_with_time_filter(
                        address, parent_time, end_time, limit=50
                    )
                else:
                    # 查询所有交易
                    transactions = self.collector.query_trc20_usdt_transactions(address, limit=50)

            elif validation['address_type'] == 'ERC20':
                if parent_time:
                    # 使用时间窗口查询
                    end_time = parent_time + timedelta(days=30 * self.config.time_window_months)
                    transactions = self.collector.query_erc20_usdt_transactions_with_time_filter(
                        address, parent_time, end_time, limit=50
                    )
                else:
                    # 查询所有交易
                    transactions = self.collector.query_erc20_usdt_transactions(address, limit=50)

            # 过滤出转出交易（from_address == address）
            outgoing_transactions = []
            for tx in transactions:
                if tx.get('from_address', '').lower() == address.lower():
                    outgoing_transactions.append(tx)

            # 按时间排序（最新的在前）
            outgoing_transactions.sort(
                key=lambda x: x.get('timestamp', datetime.min) if x.get('timestamp') else datetime.min,
                reverse=True
            )

            return outgoing_transactions

        except Exception as e:
            logger.error(f"获取地址 {address} 的USDT转出交易失败: {e}")
            return []

    def _convert_to_transaction_record(self, tx_data: Dict, level: int, parent_hash: str) -> TransactionRecord:
        """
        将交易数据转换为TransactionRecord对象

        Args:
            tx_data: 原始交易数据
            level: 层级
            parent_hash: 父交易hash

        Returns:
            TransactionRecord对象
        """
        return TransactionRecord(
            hash=tx_data.get('tx_hash', ''),
            from_address=tx_data.get('from_address', ''),
            to_address=tx_data.get('to_address', ''),
            amount=float(tx_data.get('amount_usdt', 0)),
            timestamp=tx_data.get('timestamp', datetime.now()),
            balance=0.0,  # 可以后续计算
            level=level,
            parent_hash=parent_hash
        )

    def _check_amount_filter(self, amount: float, parent_amount: float) -> bool:
        """
        检查金额过滤条件 - 按照multitrace.md要求
        金额范围: m/4 < amount <= m+1

        Args:
            amount: 当前交易金额
            parent_amount: 父交易金额

        Returns:
            是否符合过滤条件
        """
        min_amount = parent_amount * self.config.min_amount_filter_ratio  # m/4
        max_amount = parent_amount + self.config.max_amount_ratio  # m+1

        return min_amount < amount <= max_amount

    def _generate_tracking_result(self, start_address: str) -> Dict:
        """
        生成追踪结果 - 详细的分析报告

        Args:
            start_address: 起始地址

        Returns:
            追踪结果字典
        """
        # 统计信息
        total_amount = sum(tx.amount for tx in self.all_transactions)
        unique_addresses = len(set(tx.to_address for tx in self.all_transactions))
        max_level = max((tx.level for tx in self.all_transactions), default=0)

        # 按层级分组统计
        level_stats = {}
        for level in range(max_level + 1):
            level_txs = [tx for tx in self.all_transactions if tx.level == level]
            level_stats[level] = {
                'transaction_count': len(level_txs),
                'total_amount': sum(tx.amount for tx in level_txs),
                'unique_addresses': len(set(tx.to_address for tx in level_txs)),
                'addresses': list(set(tx.to_address for tx in level_txs))
            }

        # 生成地址链路径
        address_chains = self._build_address_chains()

        # 打印结果摘要
        print(f"\n📊 追踪结果摘要")
        print("-" * 40)
        print(f"起始地址: {start_address}")
        print(f"总交易数: {len(self.all_transactions)}")
        print(f"总金额: {total_amount:.6f} USDT")
        print(f"涉及地址数: {unique_addresses}")
        print(f"最大追踪层级: {max_level}")
        print(f"地址链条数: {len(address_chains)}")

        print(f"\n📈 各层级统计:")
        for level, stats in level_stats.items():
            print(f"  第{level}层: {stats['transaction_count']}笔交易, "
                  f"{stats['total_amount']:.6f} USDT, "
                  f"{stats['unique_addresses']}个地址")

        return {
            'success': True,
            'start_address': start_address,
            'config': {
                'time_window_months': self.config.time_window_months,
                'max_levels': self.config.max_levels,
                'min_amount_ratio': self.config.min_amount_ratio,
                'max_amount_ratio': self.config.max_amount_ratio,
                'min_amount_filter_ratio': self.config.min_amount_filter_ratio,
                'api_delay': self.config.api_delay
            },
            'summary': {
                'total_transactions': len(self.all_transactions),
                'total_amount': total_amount,
                'unique_addresses': unique_addresses,
                'max_level_reached': max_level,
                'tracked_addresses_count': len(self.tracked_addresses),
                'address_chains_count': len(address_chains)
            },
            'level_statistics': level_stats,
            'address_chains': address_chains,
            'transactions': [tx.to_dict() for tx in self.all_transactions]
        }

    def _build_address_chains(self) -> List[List[str]]:
        """
        构建地址链路径

        Returns:
            地址链列表
        """
        chains = []

        # 找到所有起始交易（level=0）
        start_transactions = [tx for tx in self.all_transactions if tx.level == 0]

        for start_tx in start_transactions:
            chain = [start_tx.from_address, start_tx.to_address]
            self._build_chain_recursive(start_tx.hash, chain)
            chains.append(chain)

        return chains

    def _build_chain_recursive(self, parent_hash: str, chain: List[str]):
        """
        递归构建地址链

        Args:
            parent_hash: 父交易hash
            chain: 当前链
        """
        # 找到以parent_hash为父交易的所有子交易
        child_transactions = [tx for tx in self.all_transactions if tx.parent_hash == parent_hash]

        for child_tx in child_transactions:
            if child_tx.to_address not in chain:  # 避免循环
                chain.append(child_tx.to_address)
                self._build_chain_recursive(child_tx.hash, chain)

    def save_tracking_result(self, result: Dict, output_dir: str = "data/output") -> str:
        """
        保存追踪结果到文件

        Args:
            result: 追踪结果
            output_dir: 输出目录

        Returns:
            保存的文件路径
        """
        from pathlib import Path
        import json

        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        start_addr_short = result['start_address'][:16] if result.get('start_address') else 'unknown'
        filename = f"fund_flow_tracking_{start_addr_short}_{timestamp}.json"
        file_path = output_path / filename

        # 保存JSON结果
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)

        # 生成可读报告
        report_filename = f"fund_flow_report_{start_addr_short}_{timestamp}.txt"
        report_path = output_path / report_filename

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("MoneyTrace 资金流向追踪报告\n")
            f.write("=" * 50 + "\n\n")

            if result['success']:
                f.write(f"起始地址: {result['start_address']}\n")
                f.write(f"追踪时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("配置参数:\n")
                config = result['config']
                f.write(f"  时间窗口: {config['time_window_months']} 月\n")
                f.write(f"  最大层级: {config['max_levels']}\n")
                f.write(f"  最小金额比例: {config['min_amount_ratio']}\n")
                f.write(f"  API延迟: {config['api_delay']} 秒\n\n")

                f.write("追踪结果:\n")
                summary = result['summary']
                f.write(f"  总交易数: {summary['total_transactions']}\n")
                f.write(f"  总金额: {summary['total_amount']:.6f} USDT\n")
                f.write(f"  涉及地址数: {summary['unique_addresses']}\n")
                f.write(f"  最大层级: {summary['max_level_reached']}\n")
                f.write(f"  地址链条数: {summary['address_chains_count']}\n\n")

                f.write("各层级统计:\n")
                for level, stats in result['level_statistics'].items():
                    f.write(f"  第{level}层: {stats['transaction_count']}笔交易, "
                           f"{stats['total_amount']:.6f} USDT, "
                           f"{stats['unique_addresses']}个地址\n")

                f.write("\n地址链路径:\n")
                for i, chain in enumerate(result['address_chains'], 1):
                    f.write(f"  链条{i}: {' → '.join(chain)}\n")

                f.write("\n详细交易记录:\n")
                for i, tx in enumerate(result['transactions'], 1):
                    f.write(f"  交易{i} (第{tx['level']}层):\n")
                    f.write(f"    哈希: {tx['hash']}\n")
                    f.write(f"    发送方: {tx['from_address']}\n")
                    f.write(f"    接收方: {tx['to_address']}\n")
                    f.write(f"    金额: {tx['amount']:.6f} USDT\n")
                    f.write(f"    时间: {tx['timestamp']}\n")
                    f.write(f"    父交易: {tx['parent_hash']}\n\n")
            else:
                f.write(f"追踪失败: {result['error']}\n")

        print(f"\n💾 结果已保存:")
        print(f"  JSON文件: {file_path}")
        print(f"  报告文件: {report_path}")

        return str(file_path)

def main():
    """主函数 - 测试资金流向追踪功能"""
    print("MoneyTrace 资金流向追踪模块测试")
    print("=" * 80)

    # 测试配置
    config = TrackingConfig(
        time_window_months=2,
        max_levels=4,
        min_amount_ratio=0.125,  # 1/8
        min_amount_filter_ratio=0.25,  # 1/4
        api_delay=2.0,
        max_transactions_per_level=5
    )

    # 创建追踪器
    tracker = FundFlowTracker(config)

    # 测试地址（小江的第一个地址 - 来自multitrace.md）
    test_address = "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew"

    print(f"测试地址: {test_address}")
    print("这是小江的第一个TRC20地址，将按照multitrace.md需求进行追踪")

    # 执行追踪
    result = tracker.track_fund_flow(test_address)

    # 显示结果
    if result['success']:
        print(f"\n🎉 追踪成功完成！")

        # 保存结果
        saved_file = tracker.save_tracking_result(result)
        print(f"详细结果已保存到: {saved_file}")

        # 显示关键发现
        if result['summary']['total_transactions'] > 0:
            print(f"\n🔍 关键发现:")
            print(f"  发现了 {result['summary']['total_transactions']} 笔可疑资金流向")
            print(f"  总金额: {result['summary']['total_amount']:.6f} USDT")
            print(f"  涉及 {result['summary']['unique_addresses']} 个地址")
            print(f"  最深追踪到第 {result['summary']['max_level_reached']} 层")

            if result['address_chains']:
                print(f"\n📍 资金流向路径:")
                for i, chain in enumerate(result['address_chains'][:3], 1):  # 只显示前3条
                    print(f"  路径{i}: {chain[0][:16]}... → {chain[1][:16]}... → ...")
                    if len(chain) > 2:
                        print(f"         → {chain[-1][:16]}... (共{len(chain)}个地址)")
        else:
            print(f"\n📝 未发现符合条件的资金流向")
    else:
        print(f"\n❌ 追踪失败: {result['error']}")
        if result.get('partial_results'):
            print(f"部分结果: 发现 {len(result.get('transactions', []))} 笔交易")

def test_with_custom_address():
    """使用自定义地址测试"""
    print("\n" + "="*80)
    print("自定义地址测试")
    print("="*80)

    # 在这里修改要测试的地址
    custom_address = input("请输入要测试的地址 (回车使用默认地址): ").strip()

    if not custom_address:
        custom_address = "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew"

    # 自定义配置
    config = TrackingConfig(
        time_window_months=1,  # 缩短时间窗口用于测试
        max_levels=3,          # 减少层级用于测试
        min_amount_ratio=0.1,  # 1/10
        min_amount_filter_ratio=0.2,  # 1/5
        api_delay=1.0,         # 减少延迟用于测试
        max_transactions_per_level=3
    )

    tracker = FundFlowTracker(config)
    result = tracker.track_fund_flow(custom_address)

    if result['success']:
        tracker.save_tracking_result(result)

    return result

if __name__ == "__main__":
    # 运行主测试
    main()

    # 可选：运行自定义地址测试
    # test_with_custom_address()

    def track_fund_flow(self, start_address: str) -> Dict:
        """
        追踪资金流向

        Args:
            start_address: 起始地址

        Returns:
            追踪结果字典
        """
        logger.info(f"开始追踪地址 {start_address} 的资金流向...")
        logger.info(f"配置参数: 时间窗口={self.config.time_window_months}月, "
                   f"最大层级={self.config.max_levels}, "
                   f"最小金额比例={self.config.min_amount_ratio}")

        # 重置状态
        self.tracked_addresses.clear()
        self.all_transactions.clear()

        # 验证起始地址
        validation_result = self.validator.validate_address(start_address)
        if not validation_result['format_valid']:
            return {
                'success': False,
                'error': f'起始地址 {start_address} 格式无效',
                'transactions': [],
                'summary': {},
                'validation': validation_result
            }

        try:
            # 开始递归追踪
            self._track_address_recursive(start_address, level=0, parent_amount=None, parent_hash="", parent_time=None)

            # 生成追踪结果
            result = self._generate_tracking_result(start_address)

            logger.info(f"追踪完成，共发现 {len(self.all_transactions)} 笔交易，"
                       f"涉及 {len(self.tracked_addresses)} 个地址")
            return result

        except Exception as e:
            logger.error(f"追踪过程中发生错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'transactions': [tx.to_dict() for tx in self.all_transactions],
                'summary': {},
                'partial_results': True
            }

    def _track_address_recursive(self, address: str, level: int,
                                parent_amount: Optional[float], parent_hash: str, parent_time: Optional[datetime]):
        """
        递归追踪地址

        Args:
            address: 当前地址
            level: 当前层级
            parent_amount: 父交易金额
            parent_hash: 父交易hash
            parent_time: 父交易时间
        """
        # 检查终止条件
        if level > self.config.max_levels:
            logger.info(f"达到最大层级 {self.config.max_levels}，停止追踪地址 {address}")
            return

        if address in self.tracked_addresses:
            logger.info(f"地址 {address} 已追踪过，跳过")
            return

        # 标记地址已追踪
        self.tracked_addresses.add(address)

        logger.info(f"追踪第 {level} 层地址: {address}")

        # 获取地址的USDT转出交易
        transactions = self._get_outgoing_usdt_transactions(address, parent_time)

        if not transactions:
            logger.info(f"地址 {address} 没有USDT转出交易")
            return

        # 如果是第一层，只取第一条交易（按照multitrace.md要求）
        if level == 0:
            transactions = transactions[:1]
            logger.info(f"第一层地址，只处理第一条交易")
        else:
            # 其他层级限制交易数量
            transactions = transactions[:self.config.max_transactions_per_level]

        for tx_data in transactions:
            # 转换为TransactionRecord对象
            tx = self._convert_to_transaction_record(tx_data, level, parent_hash)

            # 检查金额过滤条件
            if parent_amount is not None:
                if not self._check_amount_filter(tx.amount, parent_amount):
                    logger.debug(f"交易金额 {tx.amount} 不符合过滤条件，跳过")
                    continue

                # 检查最小金额终止条件
                if tx.amount < parent_amount * self.config.min_amount_ratio:
                    logger.info(f"交易金额 {tx.amount} 小于最小比例 {parent_amount * self.config.min_amount_ratio:.6f}，停止追踪")
                    continue

            # 记录交易
            self.all_transactions.append(tx)

            logger.info(f"记录交易: {tx.hash[:16]}... "
                       f"金额: {tx.amount:.6f} USDT "
                       f"到: {tx.to_address[:16]}...")

            # API调用延迟
            time.sleep(self.config.api_delay)

            # 递归追踪下一层
            self._track_address_recursive(
                tx.to_address,
                level + 1,
                tx.amount,
                tx.hash,
                tx.timestamp
            )