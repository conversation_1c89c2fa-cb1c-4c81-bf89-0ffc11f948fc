#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向追踪模块
根据multitrace.md需求实现多层级资金流向追踪功能

需求分析：
1. 从小江的第一个地址 TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew 开始
2. 获取该地址转出的所有USDT交易，先取第一条
3. 记录交易hash、金额m、时间t、对方地址b
4. 对地址b查询t后2个月内转出的USDT交易
5. 只记录金额在 m/4 < amount <= m+1 范围内的交易
6. 递归追踪直到：没有新地址 OR 地址链接数>4 OR 转移金额<m/8
7. 所有参数可配置：2个月、地址链接数4、1/8金额等
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Set
import logging
import time
from dataclasses import dataclass

try:
    from .address_validator import AddressValidator
    from .address_collector import AddressCollector
    from .data_reader import ExcelDataReader
except ImportError:
    from address_validator import AddressValidator
    from address_collector import AddressCollector
    from data_reader import ExcelDataReader

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TransactionRecord:
    """交易记录数据类"""
    hash: str
    from_address: str
    to_address: str
    amount: float
    timestamp: datetime
    balance: float = 0.0
    level: int = 0  # 追踪层级
    parent_hash: str = ""  # 父交易hash

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'hash': self.hash,
            'from_address': self.from_address,
            'to_address': self.to_address,
            'amount': self.amount,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'balance': self.balance,
            'level': self.level,
            'parent_hash': self.parent_hash
        }

@dataclass
class TrackingConfig:
    """追踪配置参数"""
    time_window_months: int = 2  # 时间窗口（月）
    max_levels: int = 4  # 最大地址链接数
    min_amount_ratio: float = 0.125  # 最小金额比例（1/8）
    max_amount_ratio: float = 1.0  # 最大金额比例（m+1倍）
    min_amount_filter_ratio: float = 0.25  # 最小过滤金额比例（1/4）
    api_delay: float = 2.0  # API调用延迟（秒）
    max_transactions_per_level: int = 10  # 每层最大交易数

class FundFlowTracker:
    """资金流向追踪器"""

    def __init__(self, config: Optional[TrackingConfig] = None):
        """
        初始化追踪器

        Args:
            config: 追踪配置参数
        """
        self.config = config or TrackingConfig()
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
        self.tracked_addresses: Set[str] = set()
        self.all_transactions: List[TransactionRecord] = []

    def track_fund_flow(self, start_address: str) -> Dict:
        """
        追踪资金流向

        Args:
            start_address: 起始地址

        Returns:
            追踪结果字典
        """
        logger.info(f"开始追踪地址 {start_address} 的资金流向...")
        logger.info(f"配置参数: 时间窗口={self.config.time_window_months}月, "
                   f"最大层级={self.config.max_levels}, "
                   f"最小金额比例={self.config.min_amount_ratio}")

        # 重置状态
        self.tracked_addresses.clear()
        self.all_transactions.clear()

        # 验证起始地址
        validation_result = self.validator.validate_address(start_address)
        if not validation_result['format_valid']:
            return {
                'success': False,
                'error': f'起始地址 {start_address} 格式无效',
                'transactions': [],
                'summary': {},
                'validation': validation_result
            }

        try:
            # 开始递归追踪
            self._track_address_recursive(start_address, level=0, parent_amount=None, parent_hash="", parent_time=None)

            # 生成追踪结果
            result = self._generate_tracking_result(start_address)

            logger.info(f"追踪完成，共发现 {len(self.all_transactions)} 笔交易，"
                       f"涉及 {len(self.tracked_addresses)} 个地址")
            return result

        except Exception as e:
            logger.error(f"追踪过程中发生错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'transactions': [tx.to_dict() for tx in self.all_transactions],
                'summary': {},
                'partial_results': True
            }

    def _track_address_recursive(self, address: str, level: int,
                                parent_amount: Optional[float], parent_hash: str, parent_time: Optional[datetime]):
        """
        递归追踪地址

        Args:
            address: 当前地址
            level: 当前层级
            parent_amount: 父交易金额
            parent_hash: 父交易hash
            parent_time: 父交易时间
        """
        # 检查终止条件
        if level > self.config.max_levels:
            logger.info(f"达到最大层级 {self.config.max_levels}，停止追踪地址 {address}")
            return

        if address in self.tracked_addresses:
            logger.info(f"地址 {address} 已追踪过，跳过")
            return

        # 标记地址已追踪
        self.tracked_addresses.add(address)

        logger.info(f"追踪第 {level} 层地址: {address}")

        # 获取地址的USDT转出交易
        transactions = self._get_outgoing_usdt_transactions(address, parent_time)

        if not transactions:
            logger.info(f"地址 {address} 没有USDT转出交易")
            return

        # 如果是第一层，只取第一条交易（按照multitrace.md要求）
        if level == 0:
            transactions = transactions[:1]
            logger.info(f"第一层地址，只处理第一条交易")
        else:
            # 其他层级限制交易数量
            transactions = transactions[:self.config.max_transactions_per_level]

        for tx_data in transactions:
            # 转换为TransactionRecord对象
            tx = self._convert_to_transaction_record(tx_data, level, parent_hash)

            # 检查金额过滤条件
            if parent_amount is not None:
                if not self._check_amount_filter(tx.amount, parent_amount):
                    logger.debug(f"交易金额 {tx.amount} 不符合过滤条件，跳过")
                    continue

                # 检查最小金额终止条件
                if tx.amount < parent_amount * self.config.min_amount_ratio:
                    logger.info(f"交易金额 {tx.amount} 小于最小比例 {parent_amount * self.config.min_amount_ratio:.6f}，停止追踪")
                    continue

            # 记录交易
            self.all_transactions.append(tx)

            logger.info(f"记录交易: {tx.hash[:16]}... "
                       f"金额: {tx.amount:.6f} USDT "
                       f"到: {tx.to_address[:16]}...")

            # API调用延迟
            time.sleep(self.config.api_delay)

            # 递归追踪下一层
            self._track_address_recursive(
                tx.to_address,
                level + 1,
                tx.amount,
                tx.hash,
                tx.timestamp
            )