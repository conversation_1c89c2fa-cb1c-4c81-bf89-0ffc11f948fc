#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MoneyTrace USDT交易查询 - Tkinter GUI界面
提供简洁实用的桌面应用界面，方便根据地址和时间查询USDT交易记录
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import threading
import csv
import sys
from pathlib import Path

# 添加模块路径
sys.path.append(str(Path(__file__).parent))

try:
    from modules.address_validator import AddressValidator
    from modules.address_collector import AddressCollector
    from modules.data_reader import ExcelDataReader
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

class MoneyTraceGUI:
    """MoneyTrace USDT交易查询GUI主类"""
    
    def __init__(self, root):
        """初始化GUI界面"""
        self.root = root
        self.root.title("MoneyTrace USDT交易查询工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 初始化组件
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
        
        # 存储查询结果
        self.current_results = []
        
        # 创建界面
        self.create_widgets()
        
        # 设置样式
        self.setup_styles()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        
        # 配置按钮样式
        style.configure('Query.TButton', font=('Arial', 10, 'bold'))
        style.configure('Export.TButton', font=('Arial', 9))
        
        # 配置标签样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 9))
    
    def create_widgets(self):
        """创建所有界面组件"""
        # 配置根窗口网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=2)  # Tab区域（占更多空间）
        self.root.rowconfigure(1, weight=1)  # 输出区域
        self.root.rowconfigure(2, weight=0)  # 状态栏（固定高度）
        
        # 创建主Tab控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=(5, 0))
        
        # 创建单地址查询Tab
        self.create_single_query_tab()
        
        # 创建新的Tab页面
        self.create_new_tab()
        
        # 创建共用的输出区域（在所有Tab下方）
        self.create_shared_output_section()
        
        # 创建状态栏
        self.create_shared_status_section()
    
    def create_single_query_tab(self):
        """创建单地址查询Tab页面"""
        # 单地址查询框架
        single_query_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(single_query_frame, text="单地址查询")
        
        # 配置网格权重
        single_query_frame.columnconfigure(1, weight=1)
        single_query_frame.rowconfigure(2, weight=1)  # 结果区域 - 调整行号
        
        # 移除标题，节省空间
        
        # 查询参数区域 - 调整行号
        self.create_query_section(single_query_frame, start_row=0)
        
        # 操作按钮区域 - 调整行号
        self.create_button_section(single_query_frame, start_row=1)
        
        # 结果显示区域 - 调整行号，增大表格
        self.create_results_section(single_query_frame, start_row=2)
    
    def create_new_tab(self):
        """创建新的Tab页面"""
        # 新Tab框架
        new_tab_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(new_tab_frame, text="批量分析")
        
        # 配置网格权重
        new_tab_frame.columnconfigure(0, weight=1)
        new_tab_frame.rowconfigure(0, weight=1)
        
        # 创建居中的内容框架
        content_frame = ttk.Frame(new_tab_frame)
        content_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(content_frame, text="批量分析功能", 
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(50, 30))
        
        # Hello消息
        hello_label = ttk.Label(content_frame, text="Hello! 这里将是批量分析功能页面", 
                               font=('Arial', 14))
        hello_label.grid(row=1, column=0, pady=20)
        
        # 功能说明
        info_text = """
        即将支持的功能：
        • 批量地址验证
        • 资金流向分析
        • 交易路径追踪
        • 数据统计报告
        """
        
        info_label = ttk.Label(content_frame, text=info_text, 
                              font=('Arial', 10), justify=tk.CENTER)
        info_label.grid(row=2, column=0, pady=20)
        
        # 占位按钮
        placeholder_btn = ttk.Button(content_frame, text="功能开发中...", 
                                   state='disabled')
        placeholder_btn.grid(row=3, column=0, pady=20)
    
    def create_shared_output_section(self):
        """创建共用的输出区域"""
        # 输出框架
        output_frame = ttk.LabelFrame(self.root, text="系统输出", padding="5")
        output_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=(0, 5))
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        
        # 创建文本框和滚动条
        self.output_text = tk.Text(output_frame, height=16, wrap=tk.WORD, 
                                  font=('Consolas', 9), bg='#f8f8f8', fg='#333333')
        
        output_scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        self.output_text.configure(yscrollcommand=output_scrollbar.set)
        
        # 布局
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        output_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 输出控制按钮框架
        output_control_frame = ttk.Frame(output_frame)
        output_control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 清空输出按钮
        clear_output_btn = ttk.Button(output_control_frame, text="清空输出", 
                                     command=self.clear_output)
        clear_output_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存输出按钮
        save_output_btn = ttk.Button(output_control_frame, text="保存日志", 
                                    command=self.save_output)
        save_output_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_check = ttk.Checkbutton(output_control_frame, text="自动滚动", 
                                          variable=self.auto_scroll_var)
        auto_scroll_check.pack(side=tk.RIGHT)
        
        # 初始化输出
        self.sout("MoneyTrace USDT交易查询工具已启动")
        self.sout("提示: 请选择相应的Tab页面开始使用")
    
    def create_shared_status_section(self):
        """创建共用的状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=5, pady=(0, 5))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                               style='Status.TLabel', relief=tk.SUNKEN)
        status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
    
    def create_query_section(self, parent, start_row=1):
        """创建查询参数区域"""
        # 查询参数框架
        query_frame = ttk.LabelFrame(parent, text="查询参数", padding="10")
        query_frame.grid(row=start_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        query_frame.columnconfigure(1, weight=1)
        
        # 地址输入
        ttk.Label(query_frame, text="地址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.address_var = tk.StringVar()
        self.address_entry = ttk.Entry(query_frame, textvariable=self.address_var, width=50)
        self.address_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 地址验证按钮
        self.validate_btn = ttk.Button(query_frame, text="验证", command=self.validate_address)
        self.validate_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 地址状态标签
        self.address_status_var = tk.StringVar(value="请输入地址")
        self.address_status_label = ttk.Label(query_frame, textvariable=self.address_status_var, 
                                            foreground="gray")
        self.address_status_label.grid(row=0, column=3, sticky=tk.W)
        
        # 时间和条数控制行
        ttk.Label(query_frame, text="时间范围:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        # 时间和条数控制框架
        time_control_frame = ttk.Frame(query_frame)
        time_control_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        
        # 开始时间
        ttk.Label(time_control_frame, text="从").pack(side=tk.LEFT, padx=(0, 5))
        self.start_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.start_time_var = tk.StringVar(value="00:00:00")
        
        ttk.Entry(time_control_frame, textvariable=self.start_date_var, width=12).pack(side=tk.LEFT, padx=(0, 3))
        ttk.Entry(time_control_frame, textvariable=self.start_time_var, width=10).pack(side=tk.LEFT, padx=(0, 10))
        
        # 结束时间
        ttk.Label(time_control_frame, text="到").pack(side=tk.LEFT, padx=(0, 5))
        self.end_date_var = tk.StringVar(value=(datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"))
        self.end_time_var = tk.StringVar(value="23:59:59")
        
        ttk.Entry(time_control_frame, textvariable=self.end_date_var, width=12).pack(side=tk.LEFT, padx=(0, 3))
        ttk.Entry(time_control_frame, textvariable=self.end_time_var, width=10).pack(side=tk.LEFT, padx=(0, 15))
        
        # 查询限制
        ttk.Label(time_control_frame, text="限制").pack(side=tk.LEFT, padx=(0, 5))
        self.limit_var = tk.StringVar(value="50")
        limit_spinbox = ttk.Spinbox(time_control_frame, from_=10, to=200, textvariable=self.limit_var, width=8)
        limit_spinbox.pack(side=tk.LEFT, padx=(0, 3))
        ttk.Label(time_control_frame, text="条").pack(side=tk.LEFT)
    
    def create_button_section(self, parent, start_row=2):
        """创建操作按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=start_row, column=0, columnspan=3, pady=(0, 10))
        
        # 查询按钮
        self.query_btn = ttk.Button(button_frame, text="🔍 查询USDT交易", 
                                   command=self.start_query, style='Query.TButton')
        self.query_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 导出按钮
        self.export_btn = ttk.Button(button_frame, text="📤 导出结果", 
                                    command=self.export_results, style='Export.TButton')
        self.export_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.export_btn.config(state='disabled')
        
        # 清空按钮
        self.clear_btn = ttk.Button(button_frame, text="🗑️ 清空结果", 
                                   command=self.clear_results)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(button_frame, variable=self.progress_var, 
                                          mode='indeterminate', length=200)
        self.progress_bar.pack(side=tk.LEFT, padx=(20, 0))
    
    def create_results_section(self, parent, start_row=3):
        """创建结果显示区域"""
        # 结果框架
        results_frame = ttk.LabelFrame(parent, text="交易记录", padding="5")
        results_frame.grid(row=start_row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview表格
        columns = ('时间', '发送方', '接收方', '金额(USDT)', '方向', '区块', '哈希')
        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=20)
        
        # 设置列标题和宽度
        column_widths = {'时间': 130, '发送方': 120, '接收方': 120, '金额(USDT)': 100, 
                        '方向': 50, '区块': 80, '哈希': 150}
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局表格和滚动条
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 统计信息框架
        stats_frame = ttk.Frame(results_frame)
        stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.stats_var = tk.StringVar(value="等待查询...")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var, font=('Arial', 10, 'bold'))
        stats_label.pack(side=tk.LEFT)
    

    
    def validate_address(self):
        """验证地址有效性"""
        address = self.address_var.get().strip()
        if not address:
            self.address_status_var.set("请输入地址")
            self.address_status_label.config(foreground="red")
            self.sout("请输入要验证的地址", "WARNING")
            return
        
        self.sout(f"开始验证地址: {address}")
        self.status_var.set("正在验证地址...")
        self.validate_btn.config(state='disabled')
        
        def validate_thread():
            try:
                result = self.validator.validate_address(address)
                
                # 更新UI（在主线程中）
                self.root.after(0, self.update_validation_result, result)
            except Exception as e:
                self.root.after(0, self.update_validation_error, str(e))
        
        threading.Thread(target=validate_thread, daemon=True).start()
    
    def update_validation_result(self, result):
        """更新地址验证结果"""
        self.validate_btn.config(state='normal')
        
        if result['format_valid'] and result['exists_on_chain']:
            self.address_status_var.set(f"✓ {result['address_type']} 地址有效")
            self.address_status_label.config(foreground="green")
            self.status_var.set("地址验证成功")
            self.sout(f"地址验证成功: {result['address_type']} 地址有效", "SUCCESS")
            
            # 显示链上信息
            if result.get('chain_info'):
                info = result['chain_info']
                if 'balance' in info:
                    self.sout(f"链上余额: {info.get('trx_balance', 'N/A')} TRX")
                if 'transaction_count' in info:
                    self.sout(f"交易数量: {info.get('transaction_count', 'N/A')} 笔")
                    
        elif result['format_valid']:
            self.address_status_var.set(f"⚠ {result['address_type']} 格式正确，但链上不存在")
            self.address_status_label.config(foreground="orange")
            self.status_var.set("地址格式正确但链上不存在")
            self.sout(f"地址格式正确但链上不存在: {result['address_type']}", "WARNING")
        else:
            self.address_status_var.set("✗ 地址格式无效")
            self.address_status_label.config(foreground="red")
            self.status_var.set("地址格式无效")
            self.sout("地址格式无效，请检查输入", "ERROR")
            
        # 显示错误信息
        if result.get('errors'):
            for error in result['errors']:
                self.sout(f"验证错误: {error}", "ERROR")
    
    def update_validation_error(self, error):
        """更新地址验证错误"""
        self.validate_btn.config(state='normal')
        self.address_status_var.set(f"验证失败: {error}")
        self.address_status_label.config(foreground="red")
        self.status_var.set("地址验证失败")
        self.sout(f"地址验证失败: {error}", "ERROR")
    
    def start_query(self):
        """开始查询交易"""
        address = self.address_var.get().strip()
        if not address:
            messagebox.showerror("错误", "请输入要查询的地址")
            self.sout("请输入要查询的地址", "WARNING")
            return
        
        try:
            limit = int(self.limit_var.get())
        except ValueError:
            messagebox.showerror("错误", "查询限制必须是数字")
            self.sout("查询限制必须是数字", "ERROR")
            return
        
        self.sout(f"开始查询USDT交易: {address}")
        self.sout(f"查询参数: 限制 {limit} 条记录")
        
        # 禁用查询按钮，显示进度条
        self.query_btn.config(state='disabled')
        self.progress_bar.start()
        self.status_var.set("正在查询交易记录...")
        
        def query_thread():
            try:
                # 验证地址
                self.root.after(0, lambda: self.sout("正在验证地址格式..."))
                validation = self.validator.validate_address(address)
                if not validation['format_valid']:
                    self.root.after(0, self.query_error, "地址格式无效")
                    return
                
                self.root.after(0, lambda: self.sout(f"地址类型: {validation['address_type']}"))
                
                # 解析时间参数
                start_timestamp = None
                end_timestamp = None
                
                try:
                    start_date = self.start_date_var.get().strip()
                    start_time = self.start_time_var.get().strip()
                    end_date = self.end_date_var.get().strip()
                    end_time = self.end_time_var.get().strip()
                    
                    if start_date and start_time:
                        start_timestamp = datetime.strptime(f"{start_date} {start_time}", "%Y-%m-%d %H:%M:%S")
                    
                    if end_date and end_time:
                        end_timestamp = datetime.strptime(f"{end_date} {end_time}", "%Y-%m-%d %H:%M:%S")
                        
                except ValueError as e:
                    self.root.after(0, self.query_error, f"时间格式错误: {e}")
                    return
                
                # 查询交易（传递时间参数）
                if validation['address_type'] == 'TRC20':
                    self.root.after(0, lambda: self.sout("正在查询TRC20 USDT交易..."))
                    transactions = self.collector.query_trc20_usdt_transactions(address, limit=limit)
                elif validation['address_type'] == 'ERC20':
                    self.root.after(0, lambda: self.sout("正在查询ERC20 USDT交易..."))
                    transactions = self.collector.query_erc20_usdt_transactions(address, limit=limit)
                else:
                    self.root.after(0, self.query_error, "不支持的地址类型")
                    return
                
                # 更新结果
                self.root.after(0, self.update_query_results, transactions, address)
                
            except Exception as e:
                self.root.after(0, self.query_error, str(e))
        
        threading.Thread(target=query_thread, daemon=True).start()
    
    def update_query_results(self, transactions, address):
        """更新查询结果"""
        self.query_btn.config(state='normal')
        self.progress_bar.stop()
        
        self.sout(f"查询完成，找到 {len(transactions)} 条USDT交易记录", "SUCCESS")
        
        # 清空现有结果
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 存储结果
        self.current_results = transactions
        
        # 统计信息
        total_in = 0
        total_out = 0
        in_count = 0
        out_count = 0
        
        # 添加交易记录到表格
        for tx in transactions:
            from_addr = tx.get('from_address', '')
            to_addr = tx.get('to_address', '')
            amount_usdt = tx.get('amount_usdt', 0)
            
            # 判断方向
            if from_addr.lower() == address.lower():
                direction = "转出"
                total_out += amount_usdt
                out_count += 1
            elif to_addr.lower() == address.lower():
                direction = "转入"
                total_in += amount_usdt
                in_count += 1
            else:
                direction = "相关"
            
            # 格式化数据
            timestamp = tx.get('timestamp', '')
            if isinstance(timestamp, datetime):
                time_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            else:
                time_str = str(timestamp)
            
            from_short = from_addr[:10] + "..." if len(from_addr) > 13 else from_addr
            to_short = to_addr[:10] + "..." if len(to_addr) > 13 else to_addr
            amount_str = f"{amount_usdt:.6f}"
            block_str = str(tx.get('block', ''))
            hash_short = tx.get('tx_hash', '')[:20] + "..." if len(tx.get('tx_hash', '')) > 23 else tx.get('tx_hash', '')
            
            # 插入行
            self.tree.insert('', 'end', values=(
                time_str, from_short, to_short, amount_str, direction, block_str, hash_short
            ))
        
        # 更新统计信息
        net_change = total_in - total_out
        stats_text = f"总计: {len(transactions)}条 | 转入: {in_count}条 ({total_in:.6f} USDT) | 转出: {out_count}条 ({total_out:.6f} USDT) | 净变化: {net_change:.6f} USDT"
        self.stats_var.set(stats_text)
        
        # 输出统计信息
        self.sout(f"交易统计: 转入 {in_count} 条 ({total_in:.6f} USDT), 转出 {out_count} 条 ({total_out:.6f} USDT)")
        self.sout(f"净余额变化: {net_change:.6f} USDT", "INFO" if net_change >= 0 else "WARNING")
        
        # 启用导出按钮
        if transactions:
            self.export_btn.config(state='normal')
            self.sout("可以点击导出按钮保存查询结果")
        
        self.status_var.set(f"查询完成，找到 {len(transactions)} 条交易记录")
    
    def query_error(self, error):
        """处理查询错误"""
        self.query_btn.config(state='normal')
        self.progress_bar.stop()
        self.status_var.set("查询失败")
        self.sout(f"查询失败: {error}", "ERROR")
        messagebox.showerror("查询失败", f"查询交易时发生错误:\n{error}")
    
    def export_results(self):
        """导出查询结果"""
        if not self.current_results:
            messagebox.showwarning("警告", "没有可导出的数据")
            self.sout("没有可导出的数据", "WARNING")
            return
        
        self.sout(f"准备导出 {len(self.current_results)} 条交易记录")
        
        # 选择保存文件
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            title="导出交易记录"
        )
        
        if not filename:
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题行
                writer.writerow(['时间', '发送方', '接收方', '金额(USDT)', '方向', '区块', '交易哈希'])
                
                # 写入数据
                address = self.address_var.get().strip().lower()
                for tx in self.current_results:
                    from_addr = tx.get('from_address', '')
                    to_addr = tx.get('to_address', '')
                    
                    # 判断方向
                    if from_addr.lower() == address:
                        direction = "转出"
                    elif to_addr.lower() == address:
                        direction = "转入"
                    else:
                        direction = "相关"
                    
                    timestamp = tx.get('timestamp', '')
                    if isinstance(timestamp, datetime):
                        time_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        time_str = str(timestamp)
                    
                    writer.writerow([
                        time_str,
                        from_addr,
                        to_addr,
                        tx.get('amount_usdt', 0),
                        direction,
                        tx.get('block', ''),
                        tx.get('tx_hash', '')
                    ])
            
            self.sout(f"交易记录已成功导出到: {filename}", "SUCCESS")
            messagebox.showinfo("成功", f"交易记录已导出到:\n{filename}")
            self.status_var.set(f"已导出 {len(self.current_results)} 条记录")
            
        except Exception as e:
            self.sout(f"导出失败: {e}", "ERROR")
            messagebox.showerror("导出失败", f"导出文件时发生错误:\n{e}")
    
    def clear_results(self):
        """清空查询结果"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 清空数据
        self.current_results = []
        
        # 重置统计信息
        self.stats_var.set("等待查询...")
        
        # 禁用导出按钮
        self.export_btn.config(state='disabled')
        
        self.sout("查询结果已清空", "INFO")
        self.status_var.set("已清空结果")
    
    def sout(self, message, level="INFO"):
        """
        系统输出函数 - 在GUI输出区域显示消息
        
        Args:
            message: 要输出的消息
            level: 消息级别 (INFO, WARNING, ERROR, SUCCESS)
        """
        if not hasattr(self, 'output_text'):
            # 如果输出区域还没创建，先打印到控制台
            print(f"[{level}] {message}")
            return
        
        # 获取当前时间
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色和前缀
        level_config = {
            "INFO": {"color": "#333333", "prefix": "ℹ"},
            "WARNING": {"color": "#ff8c00", "prefix": "⚠"},
            "ERROR": {"color": "#dc3545", "prefix": "✗"},
            "SUCCESS": {"color": "#28a745", "prefix": "✓"},
            "DEBUG": {"color": "#6c757d", "prefix": "🔍"}
        }
        
        config = level_config.get(level.upper(), level_config["INFO"])
        
        # 格式化消息
        formatted_message = f"[{timestamp}] {config['prefix']} {message}\n"
        
        # 在输出区域显示
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, formatted_message)
        
        # 设置颜色标签
        line_start = self.output_text.index(f"{tk.END}-1c linestart")
        line_end = self.output_text.index(f"{tk.END}-1c lineend")
        tag_name = f"{level.lower()}_{timestamp.replace(':', '')}"
        
        self.output_text.tag_add(tag_name, line_start, line_end)
        self.output_text.tag_config(tag_name, foreground=config["color"])
        
        self.output_text.config(state=tk.DISABLED)
        
        # 自动滚动到底部
        if self.auto_scroll_var.get():
            self.output_text.see(tk.END)
    
    def clear_output(self):
        """清空输出区域"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
        self.sout("输出已清空")
    
    def save_output(self):
        """保存输出日志到文件"""
        content = self.output_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("警告", "没有可保存的输出内容")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="保存输出日志"
        )
        
        if not filename:
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.sout(f"日志已保存到: {filename}", "SUCCESS")
            messagebox.showinfo("成功", f"日志已保存到:\n{filename}")
            
        except Exception as e:
            self.sout(f"保存日志失败: {e}", "ERROR")
            messagebox.showerror("保存失败", f"保存日志时发生错误:\n{e}")

def main():
    """主函数"""
    root = tk.Tk()
    app = MoneyTraceGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass
    
    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()