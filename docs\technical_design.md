# MoneyTrace 技术设计文档

## 技术选择方案

### 1. Excel文件处理技术选择

**实际测试结果发现**：项目中的Excel文件都是老格式的.xls文件，需要特殊处理。

#### 文件格式分析
```
xzbankin.xls: 3 sheets, 40x9    (小曾银行流水)
xzbain.xls: 1 sheets, 87x6      (小曾币安充值)
xjbain.xls: 3 sheets, 510x6     (小江币安充值)
xjbankinout.xls: 1 sheets, 287x11 (小江银行出入金)
xzbasale.xls: 3 sheets, 70x13   (小曾P2P卖出)
xjbasale.xls: 1 sheets, 222x12  (小江P2P卖出)
xzwxin.xls: 3 sheets, 15x11     (小曾微信收款)
xjwxin.xls: 3 sheets, 25x11     (小江微信收款)
```

#### 方案A: xlrd + pandas (推荐)
- **优势**:
  - xlrd专门支持老格式.xls文件
  - 读取速度快，内存占用小
  - 结合pandas进行数据分析
  - 经过实际测试验证可用
- **劣势**:
  - 需要两个库配合使用
  - xlrd不支持.xlsx格式

#### 方案B: 文件格式转换 + pandas + openpyxl
- **优势**:
  - 统一使用现代技术栈
  - pandas功能强大
- **劣势**:
  - 需要额外的转换步骤
  - 增加了复杂度

#### 方案C: 纯xlrd处理
- **优势**:
  - 最轻量级解决方案
  - 直接处理原始格式
- **劣势**:
  - 数据分析功能有限
  - 代码复杂度较高

**最终选择**: 方案A (xlrd + pandas)
- xlrd负责读取.xls文件
- pandas负责数据处理和分析
- 这是针对当前文件格式的最佳解决方案

### 2. 区块链API选择

#### 方案A: 官方API (推荐)
- **TRC20**: Tronscan API (https://apilist.tronscanapi.com)
- **ERC20**: Etherscan API (https://docs.etherscan.io)
- **优势**: 
  - 数据准确性高
  - 官方支持，稳定性好
  - 免费额度充足
- **劣势**: 
  - 有频率限制
  - 需要API Key

#### 方案B: 第三方聚合API
- **Moralis API**: 支持多链查询
- **Alchemy API**: 企业级区块链API
- **优势**: 
  - 统一接口，支持多链
  - 性能优化好
- **劣势**: 
  - 收费较高
  - 依赖第三方服务

#### 方案C: 直接节点查询
- **优势**: 
  - 无频率限制
  - 数据实时性最好
- **劣势**: 
  - 技术复杂度高
  - 需要维护节点

**推荐选择**: 方案A (官方API)

### 3. 地址验证技术选择

#### 方案A: 正则表达式 + Checksum验证 (推荐)
- **TRC20**: Base58编码验证 + 长度检查
- **ERC20**: 十六进制格式 + EIP-55 Checksum
- **优势**: 
  - 快速，本地验证
  - 准确率高
- **劣势**: 
  - 需要实现多种算法

#### 方案B: 第三方验证库
- **web3.py**: 以太坊地址验证
- **tronapi**: 波场地址验证
- **优势**: 
  - 现成的解决方案
  - 维护成本低
- **劣势**: 
  - 增加依赖
  - 可能过度设计

**推荐选择**: 方案A (正则表达式 + Checksum验证)

### 4. 数据存储选择

#### 方案A: SQLite (推荐)
- **优势**: 
  - 轻量级，无需安装
  - 支持SQL查询
  - 数据持久化
- **劣势**: 
  - 并发性能有限

#### 方案B: CSV文件
- **优势**: 
  - 简单直观
  - 易于查看和编辑
- **劣势**: 
  - 查询性能差
  - 数据完整性保障弱

#### 方案C: JSON文件
- **优势**: 
  - 结构化存储
  - 易于解析
- **劣势**: 
  - 文件大小较大
  - 查询不便

**推荐选择**: 方案A (SQLite)

### 5. 日志记录选择

#### 方案A: Python logging模块 (推荐)
- **优势**: 
  - 标准库，无需额外依赖
  - 功能完整，配置灵活
- **劣势**: 
  - 配置相对复杂

#### 方案B: loguru
- **优势**: 
  - 使用简单
  - 功能强大
- **劣势**: 
  - 第三方依赖

**推荐选择**: 方案A (Python logging模块)

## 核心算法设计

### 1. 时间交集算法
```python
def find_time_intersection(xiajiang_records, xiaozeng_records):
    """
    找到小江和小曾充值记录的时间交集
    """
    xj_start = min(record.timestamp for record in xiajiang_records)
    xj_end = max(record.timestamp for record in xiajiang_records)
    
    xz_start = min(record.timestamp for record in xiaozeng_records)
    xz_end = max(record.timestamp for record in xiaozeng_records)
    
    intersection_start = max(xj_start, xz_start)
    intersection_end = min(xj_end, xz_end)
    
    return intersection_start, intersection_end
```

### 2. 资金排除算法
```python
def exclude_p2p_funds(deposits, p2p_sales, fiat_receipts, time_window_hours=48):
    """
    排除通过P2P卖出并有对应法币收款的资金
    """
    excluded_amount = 0
    evidence_chain = []
    
    for deposit in deposits:
        # 在时间窗口内查找P2P卖出记录
        matching_sales = find_matching_p2p_sales(deposit, p2p_sales, time_window_hours)
        
        for sale in matching_sales:
            # 查找对应的法币收款记录
            matching_receipt = find_matching_fiat_receipt(sale, fiat_receipts)
            
            if matching_receipt:
                excluded_amount += deposit.amount
                evidence_chain.append({
                    'deposit': deposit,
                    'p2p_sale': sale,
                    'fiat_receipt': matching_receipt
                })
                break
    
    return excluded_amount, evidence_chain
```

## 性能优化策略

1. **批量处理**: 区块链API调用采用批量模式
2. **缓存机制**: 地址验证结果本地缓存
3. **异步处理**: 使用asyncio处理并发API请求
4. **数据索引**: SQLite数据库建立适当索引

## 错误处理策略

1. **网络异常**: 自动重试机制，指数退避
2. **数据异常**: 详细记录异常数据，继续处理其他数据
3. **API限制**: 智能频率控制，避免触发限制
4. **文件异常**: 文件格式验证，友好错误提示

## 安全考虑

1. **API密钥管理**: 使用环境变量存储敏感信息
2. **数据脱敏**: 输出报告中敏感信息脱敏处理
3. **访问控制**: 限制文件访问权限
4. **审计日志**: 记录所有关键操作

请确认以上技术选择方案，我将根据您的决定开始实现。
