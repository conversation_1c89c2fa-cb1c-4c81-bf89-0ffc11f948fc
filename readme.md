# MoneyTrace 资金流向追踪分析系统

## 项目概述

MoneyTrace 是一个专门用于追踪和分析加密货币资金流向的系统，主要目标是通过链上数据分析，证明特定资金流向的真实性，并排除不合理的关联指控。

## 核心目标

1. **资金流向追踪**：识别并量化从江育成（小江）币安存款地址，通过币安内部或外部交易，最终流入曾荣伟（小曾）币安存款地址的USDT总额。

2. **资金关联排除**：识别小江收到的、但在短时间内通过P2P卖出并有对应法币收款记录的USDT，将这部分资金从"流向小曾"的嫌疑资金中排除。

3. **资金排除分析**：小江收款地址转出的3-5次下层路径没有小曾的地址，小曾转入地址的3-5次上层路径没有小江的地址，排除嫌疑资金。

## 系统架构

### 主要模块


#### 0.简易分析模块：小江和小曾币安充值记录时间起止点注意起止点时间是个交集，期间： 小江共充值usdt-小江p2p卖出(和小江银行卡和微信收款对应） = 小江实际usdt余额 也就是 所有有可能 流向小曾地址的 嫌疑金额

先输出这个计算流程和最后得出的金额：

#### 1. 地址验证模块 (Address Validation)
- **功能**：验证PDF扫描得来的区块链地址有效性
- **输入**：各XLS文件中的地址数据
- **输出**：有效地址列表、无效地址列表及错误说明
- **关键特性**：
  - 支持TRC20/ERC20地址格式验证
  - 链上地址存在性验证
  - 错误地址分类保存

#### 2. 小曾收款地址获取模块
- **功能**：通过链上查询获取小曾所有币安充值收款地址
- **数据源**：`xzbain.xls`（小曾币安充值记录）
- **处理流程**：
  ```
  发送地址提取 → 地址验证 → 链上交易查询 → 收款地址提取 → 去重并集
  ```
- **输出文件**：
  - `xiaozeng_receive_addresses_final.txt` - 小曾收款地址（并集）
  - `valid_sender_addresses.txt` - 有效发送地址
  - `invalid_addresses.txt` - 无效地址及错误说明

#### 3. 资金流向追踪模块
- **功能**：分析资金从小江地址到小曾地址的流向路径



- **处理逻辑**：
  - 直接转账识别
  - 中间地址追踪
  - 币安内部划转分析
  - 多层级资金路径重建

#### 4. 资金排除分析模块
- **功能**：建立时间-金额关联模型，排除合理资金流向
- **排除条件**：
  - 币安收到USDT → 短时间内P2P卖出 → 法币账户收到对应款项
- **时间窗口**：收到币后几小时到1-2天内
- **验证链**：充值记录 + P2P记录 + 银行/微信收款记录

### 数据文件结构

```
├── 币安充值数据
│   ├── xzbain.xls      # 小曾币安充值记录
│   ├── xjbain.xls        # 小江币安充值记录
│   └── xjbankinout.xls   # 小江币安出入金记录
│
├── P2P交易数据
│   ├── xzbasale.xls      # 小曾P2P卖出记录
│   └── xjbasale.xls      # 小江P2P卖出记录
│
├── 法币收款数据
│   ├── xzwxin.xls        # 小曾微信收款记录
│   ├── xjwxin.xls        # 小江微信收款记录
│   └── xjbankinout.xls   # 小江银行收款记录
│
└── 源数据
    ├── coinrecord.pdf    # 原始PDF记录
    └── Transfers_20250804.csv  ，小江某个地址的链上交易记录
```

### 关键地址信息
     TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf  交易所地址A 
     TV6MuMXfmLbBqPZvBHdwFsDnQeVfnmiuSi  地址B
     小江  TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew 地址主要转账地址为A和B：
       ，

     小江 TUmBkCNdT9YzTpKqwAZwL5s92iTv6Nw8rh  全部转账地址 B： TV6MuMXfmLbBqPZvBHdwFsDnQeVfnmiuSi

    
     
#### 小江（江育成）
- **UID**: ********* / ********** / *********
- **已知收款地址** ，可能有可多，需要文件里查看：
  - `TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew` (TRC20)
  - `TUmBkCNdT9YzTpKqwAZwL5s92iTv6Nw8rh` (TRC20)
  - `******************************************` (ERC20)

#### 小曾（曾荣伟）
- **UID**: ********
- **收款地址**：通过链上查询获取

## 技术实现

### 核心技术栈
- **语言**：Python 3.10+
- **数据处理**：pandas, openpyxl
- **网络请求**：requests
- **区块链API**：
  - Tronscan API (TRC20)
  - Etherscan API (ERC20)

### 关键算法

#### 地址验证算法
```python
def validate_address(address):
    # 1. 格式验证（TRC20/ERC20）
    # 2. 链上存在性验证
    # 3. 分类处理结果
```

#### 资金路径追踪算法
```python
def trace_fund_path(source_addr, target_addr):
    # 1. 直接转账检测
    # 2. 多层级中间地址追踪
    # 3. 时间序列分析
    # 4. 资金汇聚点识别
```

#### 时间关联分析算法
```python
def correlate_transactions(crypto_tx, p2p_tx, fiat_tx):
    # 1. 时间窗口匹配
    # 2. 金额相似度计算
    # 3. 证据链完整性验证
```

## 输出报告结构



### 1. 地址验证报告
- 有效地址统计
- 无效地址列表及错误原因
- 地址类型分布

### 2. 资金流向报告
- 直接流向金额统计
- 间接流向路径分析
- 无法追踪的资金说明

### 3. 资金排除报告
- 可排除资金总额
- 排除理由详细说明
- 时间-金额关联证据

### 4. 综合分析报告
- 净流入嫌疑金额
- 关联性评估
- 反驳证据总结

## 使用流程

### 第一阶段：数据准备与验证
1. 收集并整理所有XLS数据文件
2. 运行地址验证模块
3. 生成有效地址清单

### 第二阶段：收款地址获取
1. 处理小曾币安充值记录
2. 链上查询获取收款地址
3. 生成收款地址并集

### 第三阶段：资金流向分析
1. 执行资金路径追踪
2. 分析直接和间接流向
3. 生成流向关系图

### 第四阶段：排除分析
1. 建立时间-金额关联模型
2. 匹配P2P卖出和法币收款记录
3. 量化可排除资金

### 第五阶段：报告生成
1. 汇总所有分析结果
2. 生成综合分析报告
3. 提供法律证据材料

## 项目结构

```
moneytrace/
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── main.py            # 主程序入口
│   ├── modules/           # 功能模块
│   │   ├── __init__.py
│   │   ├── data_reader.py     # Excel文件读取模块
│   │   ├── address_validator.py # 地址验证模块
│   │   ├── simple_analyzer.py   # 简易分析模块
│   │   └── address_collector.py # 收款地址获取模块
│   ├── utils/             # 工具函数
│   │   ├── __init__.py
│   │   ├── blockchain_api.py  # 区块链API接口
│   │   └── time_utils.py      # 时间处理工具
│   └── config/            # 配置文件
│       ├── __init__.py
│       └── settings.py    # 系统配置
├── docs/                  # 文档目录
│   ├── technical_design.md    # 技术设计文档
│   ├── api_reference.md       # API参考文档
│   └── user_guide.md          # 用户使用指南
├── data/                  # 数据文件目录
│   ├── input/             # 输入数据
│   └── output/            # 输出结果
├── tests/                 # 测试文件
├── requirements.txt       # 依赖包列表
└── README.md             # 项目说明
```

## 项目特点

1. **数据驱动**：基于真实的链上数据和交易记录
2. **多维度验证**：结合时间、金额、地址多重验证
3. **自动化处理**：减少人工错误，提高分析效率
4. **证据完整性**：形成完整的证据链条
5. **可追溯性**：所有分析步骤可回溯验证

## 风险控制

1. **数据质量**：PDF扫描地址需要格外验证
2. **API限制**：区块链浏览器API有频率限制
3. **时效性**：链上数据可能有延迟
4. **隐私保护**：确保敏感数据安全

## 预期成果

通过本系统的分析，将能够：
1. 明确证明资金流向的真实路径
2. 量化可以合理排除的资金金额
3. 提供具有法律效力的技术分析报告
4. 有效反驳不合理的资金关联指控