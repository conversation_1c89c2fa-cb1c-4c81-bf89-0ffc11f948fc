#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小曾收款地址分析脚本
通过链上查询获取小曾所有币安充值收款地址的独立运行脚本
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.data_reader import ExcelDataReader
from modules.xiaozeng_address_analyzer import XiaozengAddressAnalyzer
import argparse
import logging

def setup_logging(verbose=False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('xiaozeng_analysis.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='小曾收款地址分析工具')
    parser.add_argument('--data-dir', default='.', help='数据文件目录 (默认: 当前目录)')
    parser.add_argument('--output-dir', default='.', help='输出文件目录 (默认: 当前目录)')
    parser.add_argument('--time-tolerance', type=float, default=1.0, 
                       help='时间匹配容差(小时) (默认: 1.0)')
    parser.add_argument('--amount-tolerance', type=float, default=3.0,
                       help='金额匹配容差(USDT) (默认: 3.0)')
    parser.add_argument('--min-senders', type=int, default=3,
                       help='最少发送地址数量 (默认: 3)')
    parser.add_argument('--min-amount', type=float, default=100.0,
                       help='递归截止金额(USDT) (默认: 100.0)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    parser.add_argument('--no-recursive', action='store_true',
                       help='跳过递归追踪')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    try:
        print("=" * 60)
        print("小曾收款地址分析工具")
        print("=" * 60)
        print(f"数据目录: {args.data_dir}")
        print(f"输出目录: {args.output_dir}")
        print(f"分析参数: 时间±{args.time_tolerance}h, 金额±{args.amount_tolerance}USDT")
        print(f"         最少{args.min_senders}个发送地址, 递归截止{args.min_amount}USDT")
        print()
        
        # 初始化组件
        logger.info("初始化数据读取器和分析器...")
        reader = ExcelDataReader(args.data_dir)
        analyzer = XiaozengAddressAnalyzer(reader)
        
        # 设置分析参数
        analyzer.time_tolerance_hours = args.time_tolerance
        analyzer.amount_tolerance_usdt = args.amount_tolerance
        analyzer.min_sender_count = args.min_senders
        analyzer.min_amount_threshold = args.min_amount
        
        # 执行分析
        logger.info("开始执行小曾收款地址分析...")
        print("正在分析，请稍候...")
        
        result = analyzer.analyze_xiaozeng_addresses()
        
        # 保存结果
        logger.info("保存分析结果...")
        saved_files = analyzer.save_analysis_results(result, args.output_dir)
        
        # 输出结果摘要
        print("\n" + "=" * 60)
        print("分析完成")
        print("=" * 60)
        
        summary = result['summary']
        print(f"币安充值记录: {summary['total_deposit_records']} 条")
        print(f"有效发送地址: {summary['valid_sender_addresses']} 个")
        print(f"符合条件交易: {summary['total_filtered_transactions']} 条")
        print(f"真实收款地址: {summary['xiaozeng_real_addresses_count']} 个")
        print(f"递归追踪交易: {summary['recursive_trace_count']} 条")
        
        # 显示真实收款地址
        real_addresses = result['intersection_analysis']['xiaozeng_real_addresses']
        if real_addresses:
            print(f"\n小曾真实收款地址 ({len(real_addresses)} 个):")
            for i, (addr, info) in enumerate(real_addresses.items(), 1):
                print(f"{i:2d}. {addr}")
                print(f"     发送地址: {info['sender_count']}个, "
                      f"交易: {info['transaction_count']}次, "
                      f"金额: {info['total_amount']:.2f} USDT")
        
        # 显示保存的文件
        if saved_files and 'error' not in saved_files:
            print(f"\n输出文件 ({len(saved_files)} 个):")
            file_descriptions = {
                'real_addresses': '真实收款地址列表',
                'statistics': '地址统计分析',
                'transactions': '符合条件的交易记录',
                'complete_json': '完整分析结果(JSON)'
            }
            for file_type, file_path in saved_files.items():
                desc = file_descriptions.get(file_type, file_type)
                print(f"  • {desc}: {file_path}")
        elif 'error' in saved_files:
            print(f"⚠️  文件保存失败: {saved_files['error']}")
        
        print(f"\n详细日志已保存到: xiaozeng_analysis.log")
        print("分析完成！")
        
    except FileNotFoundError as e:
        print(f"❌ 文件不存在: {e}")
        print("请确保数据文件 xzbain.xls 存在于指定目录中")
        sys.exit(1)
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}", exc_info=True)
        print(f"❌ 分析失败: {e}")
        print("详细错误信息请查看日志文件: xiaozeng_analysis.log")
        sys.exit(1)

if __name__ == "__main__":
    main()