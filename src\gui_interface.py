#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面 - Tkinter桌面应用
提供原生桌面应用的地址测试界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from datetime import datetime
import threading

class AddressTestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MoneyTrace 地址测试工具")
        self.root.geometry("800x600")
        
        # 初始化工具
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="🔍 MoneyTrace 地址测试工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack()
        
        # 创建标签页
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 单地址测试标签页
        self.single_frame = ttk.Frame(notebook)
        notebook.add(self.single_frame, text="单地址测试")
        self.setup_single_tab()
        
        # 批量测试标签页
        self.batch_frame = ttk.Frame(notebook)
        notebook.add(self.batch_frame, text="批量测试")
        self.setup_batch_tab()
        
        # 交易查询标签页
        self.tx_frame = ttk.Frame(notebook)
        notebook.add(self.tx_frame, text="交易查询")
        self.setup_transaction_tab()
    
    def setup_single_tab(self):
        """设置单地址测试标签页"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.single_frame, text="地址输入")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="输入地址:").pack(anchor=tk.W, padx=5, pady=2)
        self.single_entry = ttk.Entry(input_frame, width=80)
        self.single_entry.pack(fill=tk.X, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="验证地址", 
                  command=self.validate_single).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空", 
                  command=self.clear_single).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.single_frame, text="验证结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.single_result = scrolledtext.ScrolledText(result_frame, height=15)
        self.single_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def setup_batch_tab(self):
        """设置批量测试标签页"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.batch_frame, text="地址列表输入")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="输入地址列表（每行一个）:").pack(anchor=tk.W, padx=5, pady=2)
        self.batch_text = scrolledtext.ScrolledText(input_frame, height=8)
        self.batch_text.pack(fill=tk.X, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="批量验证", 
                  command=self.validate_batch).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空", 
                  command=self.clear_batch).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="加载示例", 
                  command=self.load_sample).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.batch_frame, text="批量验证结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.batch_result = scrolledtext.ScrolledText(result_frame, height=10)
        self.batch_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def setup_transaction_tab(self):
        """设置交易查询标签页"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.tx_frame, text="交易查询")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="输入地址:").pack(anchor=tk.W, padx=5, pady=2)
        self.tx_entry = ttk.Entry(input_frame, width=80)
        self.tx_entry.pack(fill=tk.X, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="查询交易", 
                  command=self.query_transactions).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空", 
                  command=self.clear_transactions).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.tx_frame, text="交易记录")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.tx_result = scrolledtext.ScrolledText(result_frame, height=15)
        self.tx_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def validate_single(self):
        """验证单个地址"""
        address = self.single_entry.get().strip()
        if not address:
            messagebox.showwarning("警告", "请输入地址")
            return
        
        self.single_result.delete(1.0, tk.END)
        self.single_result.insert(tk.END, "验证中...\n")
        self.root.update()
        
        def validate_thread():
            try:
                result = self.validator.validate_address(address)
                
                output = f"{'='*60}\n"
                output += f"地址验证结果\n"
                output += f"{'='*60}\n"
                output += f"地址: {address}\n"
                output += f"类型: {result['address_type']}\n"
                output += f"格式有效: {'✅ 是' if result['format_valid'] else '❌ 否'}\n"
                output += f"链上存在: {'✅ 是' if result['exists_on_chain'] else '❌ 否'}\n"
                output += f"应忽略: {'⚠️ 是' if result['should_ignore'] else '✅ 否'}\n"
                
                if result['chain_info']:
                    output += f"链上信息: {result['chain_info']}\n"
                
                if result['errors']:
                    output += f"错误信息:\n"
                    for error in result['errors']:
                        output += f"  - {error}\n"
                
                # 更新UI
                self.root.after(0, lambda: self.update_single_result(output))
                
            except Exception as e:
                error_msg = f"验证失败: {str(e)}\n"
                self.root.after(0, lambda: self.update_single_result(error_msg))
        
        threading.Thread(target=validate_thread, daemon=True).start()
    
    def update_single_result(self, text):
        """更新单地址结果"""
        self.single_result.delete(1.0, tk.END)
        self.single_result.insert(tk.END, text)
    
    def validate_batch(self):
        """批量验证地址"""
        addresses_text = self.batch_text.get(1.0, tk.END).strip()
        if not addresses_text:
            messagebox.showwarning("警告", "请输入地址列表")
            return
        
        addresses = [addr.strip() for addr in addresses_text.split('\n') if addr.strip()]
        
        self.batch_result.delete(1.0, tk.END)
        self.batch_result.insert(tk.END, f"批量验证 {len(addresses)} 个地址...\n")
        self.root.update()
        
        def batch_validate_thread():
            try:
                results = self.validator.validate_address_list(addresses)
                
                output = f"{'='*60}\n"
                output += f"批量验证结果\n"
                output += f"{'='*60}\n"
                output += f"总地址数: {results['total_addresses']}\n"
                output += f"有效地址: {len(results['valid_addresses'])}\n"
                output += f"无效地址: {len(results['invalid_addresses'])}\n"
                output += f"忽略地址: {len(results['ignored_addresses'])}\n"
                output += f"TRC20地址: {len(results['trc20_addresses'])}\n"
                output += f"ERC20地址: {len(results['erc20_addresses'])}\n\n"
                
                if results['valid_addresses']:
                    output += "✅ 有效地址:\n"
                    for addr in results['valid_addresses']:
                        output += f"  - {addr}\n"
                    output += "\n"
                
                if results['invalid_addresses']:
                    output += "❌ 无效地址:\n"
                    for addr in results['invalid_addresses'][:10]:  # 只显示前10个
                        output += f"  - {addr}\n"
                    if len(results['invalid_addresses']) > 10:
                        output += f"  ... 还有 {len(results['invalid_addresses']) - 10} 个\n"
                
                self.root.after(0, lambda: self.update_batch_result(output))
                
            except Exception as e:
                error_msg = f"批量验证失败: {str(e)}\n"
                self.root.after(0, lambda: self.update_batch_result(error_msg))
        
        threading.Thread(target=batch_validate_thread, daemon=True).start()
    
    def update_batch_result(self, text):
        """更新批量结果"""
        self.batch_result.delete(1.0, tk.END)
        self.batch_result.insert(tk.END, text)
    
    def query_transactions(self):
        """查询交易记录"""
        address = self.tx_entry.get().strip()
        if not address:
            messagebox.showwarning("警告", "请输入地址")
            return
        
        self.tx_result.delete(1.0, tk.END)
        self.tx_result.insert(tk.END, "查询交易中...\n")
        self.root.update()
        
        def query_thread():
            try:
                # 先验证地址
                validation = self.validator.validate_address(address)
                
                output = f"{'='*60}\n"
                output += f"交易查询结果\n"
                output += f"{'='*60}\n"
                output += f"地址: {address}\n"
                output += f"类型: {validation['address_type']}\n"
                
                if not validation['format_valid'] or validation['should_ignore']:
                    output += "❌ 地址无效或应忽略\n"
                    self.root.after(0, lambda: self.update_tx_result(output))
                    return
                
                # 查询交易
                transactions = []
                if validation['address_type'] == 'TRC20':
                    transactions = self.collector.query_trc20_transaction(address, None)
                elif validation['address_type'] == 'ERC20':
                    transactions = self.collector.query_erc20_transaction(address, None)
                
                output += f"交易数量: {len(transactions)}\n\n"
                
                if transactions:
                    output += "📋 交易记录:\n"
                    for i, tx in enumerate(transactions[:10], 1):  # 只显示前10条
                        output += f"\n交易 {i}:\n"
                        output += f"  哈希: {tx.get('tx_hash', 'N/A')}\n"
                        output += f"  发送方: {tx.get('from_address', 'N/A')}\n"
                        output += f"  接收方: {tx.get('to_address', 'N/A')}\n"
                        output += f"  金额: {tx.get('amount', 'N/A')}\n"
                        output += f"  时间: {tx.get('timestamp', 'N/A')}\n"
                    
                    if len(transactions) > 10:
                        output += f"\n... 还有 {len(transactions) - 10} 条交易\n"
                else:
                    output += "❌ 未找到交易记录\n"
                
                self.root.after(0, lambda: self.update_tx_result(output))
                
            except Exception as e:
                error_msg = f"查询失败: {str(e)}\n"
                self.root.after(0, lambda: self.update_tx_result(error_msg))
        
        threading.Thread(target=query_thread, daemon=True).start()
    
    def update_tx_result(self, text):
        """更新交易结果"""
        self.tx_result.delete(1.0, tk.END)
        self.tx_result.insert(tk.END, text)
    
    def clear_single(self):
        """清空单地址输入"""
        self.single_entry.delete(0, tk.END)
        self.single_result.delete(1.0, tk.END)
    
    def clear_batch(self):
        """清空批量输入"""
        self.batch_text.delete(1.0, tk.END)
        self.batch_result.delete(1.0, tk.END)
    
    def clear_transactions(self):
        """清空交易查询"""
        self.tx_entry.delete(0, tk.END)
        self.tx_result.delete(1.0, tk.END)
    
    def load_sample(self):
        """加载示例地址"""
        sample_addresses = """TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew
TUmBkCNdT9YzTpKqwAZwL5s92iTv6Nw8rh
0x61f73c531c47d345ee642566d9d9fb38db287cdb
TXVL9P8ZiiABWq6D66HndrtVC2bQegve9q
135343225
96215380
invalid_address_test"""
        
        self.batch_text.delete(1.0, tk.END)
        self.batch_text.insert(tk.END, sample_addresses)

def main():
    """主函数"""
    root = tk.Tk()
    app = AddressTestGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
