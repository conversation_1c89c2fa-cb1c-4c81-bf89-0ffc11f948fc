#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面 - Tkinter桌面应用
提供原生桌面应用的地址测试界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from modules.xiaozeng_address_analyzer import XiaozengAddressAnalyzer
from datetime import datetime
import threading

class AddressTestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MoneyTrace 地址测试工具")
        self.root.geometry("800x600")
        
        # 初始化工具
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
        self.analyzer = XiaozengAddressAnalyzer(self.reader)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="🔍 MoneyTrace 地址测试工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack()
        
        # 创建标签页
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 单地址测试标签页
        self.single_frame = ttk.Frame(notebook)
        notebook.add(self.single_frame, text="单地址测试")
        self.setup_single_tab()
        
        # 批量测试标签页
        self.batch_frame = ttk.Frame(notebook)
        notebook.add(self.batch_frame, text="批量测试")
        self.setup_batch_tab()
        
        # 交易查询标签页
        self.tx_frame = ttk.Frame(notebook)
        notebook.add(self.tx_frame, text="交易查询")
        self.setup_transaction_tab()
        
        # 小曾地址分析标签页
        self.analysis_frame = ttk.Frame(notebook)
        notebook.add(self.analysis_frame, text="小曾地址分析")
        self.setup_analysis_tab()
    
    def setup_single_tab(self):
        """设置单地址测试标签页"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.single_frame, text="地址输入")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="输入地址:").pack(anchor=tk.W, padx=5, pady=2)
        self.single_entry = ttk.Entry(input_frame, width=80)
        self.single_entry.pack(fill=tk.X, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="验证地址", 
                  command=self.validate_single).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空", 
                  command=self.clear_single).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.single_frame, text="验证结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.single_result = scrolledtext.ScrolledText(result_frame, height=15)
        self.single_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def setup_batch_tab(self):
        """设置批量测试标签页"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.batch_frame, text="地址列表输入")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="输入地址列表（每行一个）:").pack(anchor=tk.W, padx=5, pady=2)
        self.batch_text = scrolledtext.ScrolledText(input_frame, height=8)
        self.batch_text.pack(fill=tk.X, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="批量验证", 
                  command=self.validate_batch).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空", 
                  command=self.clear_batch).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="加载示例", 
                  command=self.load_sample).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.batch_frame, text="批量验证结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.batch_result = scrolledtext.ScrolledText(result_frame, height=10)
        self.batch_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def setup_transaction_tab(self):
        """设置交易查询标签页"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.tx_frame, text="交易查询")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="输入地址:").pack(anchor=tk.W, padx=5, pady=2)
        self.tx_entry = ttk.Entry(input_frame, width=80)
        self.tx_entry.pack(fill=tk.X, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="查询交易", 
                  command=self.query_transactions).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空", 
                  command=self.clear_transactions).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.tx_frame, text="交易记录")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.tx_result = scrolledtext.ScrolledText(result_frame, height=15)
        self.tx_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def validate_single(self):
        """验证单个地址"""
        address = self.single_entry.get().strip()
        if not address:
            messagebox.showwarning("警告", "请输入地址")
            return
        
        self.single_result.delete(1.0, tk.END)
        self.single_result.insert(tk.END, "验证中...\n")
        self.root.update()
        
        def validate_thread():
            try:
                result = self.validator.validate_address(address)
                
                output = f"{'='*60}\n"
                output += f"地址验证结果\n"
                output += f"{'='*60}\n"
                output += f"地址: {address}\n"
                output += f"类型: {result['address_type']}\n"
                output += f"格式有效: {'✅ 是' if result['format_valid'] else '❌ 否'}\n"
                output += f"链上存在: {'✅ 是' if result['exists_on_chain'] else '❌ 否'}\n"
                output += f"应忽略: {'⚠️ 是' if result['should_ignore'] else '✅ 否'}\n"
                
                if result['chain_info']:
                    output += f"链上信息: {result['chain_info']}\n"
                
                if result['errors']:
                    output += f"错误信息:\n"
                    for error in result['errors']:
                        output += f"  - {error}\n"
                
                # 更新UI
                self.root.after(0, lambda: self.update_single_result(output))
                
            except Exception as e:
                error_msg = f"验证失败: {str(e)}\n"
                self.root.after(0, lambda: self.update_single_result(error_msg))
        
        threading.Thread(target=validate_thread, daemon=True).start()
    
    def update_single_result(self, text):
        """更新单地址结果"""
        self.single_result.delete(1.0, tk.END)
        self.single_result.insert(tk.END, text)
    
    def validate_batch(self):
        """批量验证地址"""
        addresses_text = self.batch_text.get(1.0, tk.END).strip()
        if not addresses_text:
            messagebox.showwarning("警告", "请输入地址列表")
            return
        
        addresses = [addr.strip() for addr in addresses_text.split('\n') if addr.strip()]
        
        self.batch_result.delete(1.0, tk.END)
        self.batch_result.insert(tk.END, f"批量验证 {len(addresses)} 个地址...\n")
        self.root.update()
        
        def batch_validate_thread():
            try:
                results = self.validator.validate_address_list(addresses)
                
                output = f"{'='*60}\n"
                output += f"批量验证结果\n"
                output += f"{'='*60}\n"
                output += f"总地址数: {results['total_addresses']}\n"
                output += f"有效地址: {len(results['valid_addresses'])}\n"
                output += f"无效地址: {len(results['invalid_addresses'])}\n"
                output += f"忽略地址: {len(results['ignored_addresses'])}\n"
                output += f"TRC20地址: {len(results['trc20_addresses'])}\n"
                output += f"ERC20地址: {len(results['erc20_addresses'])}\n\n"
                
                if results['valid_addresses']:
                    output += "✅ 有效地址:\n"
                    for addr in results['valid_addresses']:
                        output += f"  - {addr}\n"
                    output += "\n"
                
                if results['invalid_addresses']:
                    output += "❌ 无效地址:\n"
                    for addr in results['invalid_addresses'][:10]:  # 只显示前10个
                        output += f"  - {addr}\n"
                    if len(results['invalid_addresses']) > 10:
                        output += f"  ... 还有 {len(results['invalid_addresses']) - 10} 个\n"
                
                self.root.after(0, lambda: self.update_batch_result(output))
                
            except Exception as e:
                error_msg = f"批量验证失败: {str(e)}\n"
                self.root.after(0, lambda: self.update_batch_result(error_msg))
        
        threading.Thread(target=batch_validate_thread, daemon=True).start()
    
    def update_batch_result(self, text):
        """更新批量结果"""
        self.batch_result.delete(1.0, tk.END)
        self.batch_result.insert(tk.END, text)
    
    def query_transactions(self):
        """查询交易记录"""
        address = self.tx_entry.get().strip()
        if not address:
            messagebox.showwarning("警告", "请输入地址")
            return
        
        self.tx_result.delete(1.0, tk.END)
        self.tx_result.insert(tk.END, "查询交易中...\n")
        self.root.update()
        
        def query_thread():
            try:
                # 先验证地址
                validation = self.validator.validate_address(address)
                
                output = f"{'='*60}\n"
                output += f"交易查询结果\n"
                output += f"{'='*60}\n"
                output += f"地址: {address}\n"
                output += f"类型: {validation['address_type']}\n"
                
                if not validation['format_valid'] or validation['should_ignore']:
                    output += "❌ 地址无效或应忽略\n"
                    self.root.after(0, lambda: self.update_tx_result(output))
                    return
                
                # 查询交易
                transactions = []
                if validation['address_type'] == 'TRC20':
                    transactions = self.collector.query_trc20_transaction(address, None)
                elif validation['address_type'] == 'ERC20':
                    transactions = self.collector.query_erc20_transaction(address, None)
                
                output += f"交易数量: {len(transactions)}\n\n"
                
                if transactions:
                    output += "📋 交易记录:\n"
                    for i, tx in enumerate(transactions[:10], 1):  # 只显示前10条
                        output += f"\n交易 {i}:\n"
                        output += f"  哈希: {tx.get('tx_hash', 'N/A')}\n"
                        output += f"  发送方: {tx.get('from_address', 'N/A')}\n"
                        output += f"  接收方: {tx.get('to_address', 'N/A')}\n"
                        output += f"  金额: {tx.get('amount', 'N/A')}\n"
                        output += f"  时间: {tx.get('timestamp', 'N/A')}\n"
                    
                    if len(transactions) > 10:
                        output += f"\n... 还有 {len(transactions) - 10} 条交易\n"
                else:
                    output += "❌ 未找到交易记录\n"
                
                self.root.after(0, lambda: self.update_tx_result(output))
                
            except Exception as e:
                error_msg = f"查询失败: {str(e)}\n"
                self.root.after(0, lambda: self.update_tx_result(error_msg))
        
        threading.Thread(target=query_thread, daemon=True).start()
    
    def update_tx_result(self, text):
        """更新交易结果"""
        self.tx_result.delete(1.0, tk.END)
        self.tx_result.insert(tk.END, text)
    
    def clear_single(self):
        """清空单地址输入"""
        self.single_entry.delete(0, tk.END)
        self.single_result.delete(1.0, tk.END)
    
    def clear_batch(self):
        """清空批量输入"""
        self.batch_text.delete(1.0, tk.END)
        self.batch_result.delete(1.0, tk.END)
    
    def clear_transactions(self):
        """清空交易查询"""
        self.tx_entry.delete(0, tk.END)
        self.tx_result.delete(1.0, tk.END)
    
    def load_sample(self):
        """加载示例地址"""
        sample_addresses = """TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew
TUmBkCNdT9YzTpKqwAZwL5s92iTv6Nw8rh
0x61f73c531c47d345ee642566d9d9fb38db287cdb
TXVL9P8ZiiABWq6D66HndrtVC2bQegve9q
135343225
96215380
invalid_address_test"""
        
        self.batch_text.delete(1.0, tk.END)
        self.batch_text.insert(tk.END, sample_addresses)
    
    def setup_analysis_tab(self):
        """设置小曾地址分析标签页"""
        # 说明区域
        info_frame = ttk.LabelFrame(self.analysis_frame, text="功能说明")
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_text = """通过链上查询获取小曾所有币安充值收款地址
• 数据源：xzbain.xls（小曾币安充值记录）
• 匹配条件：时间±1小时，金额误差<3 USDT
• 交集逻辑：被≥3个不同发送地址转账的收款地址
• 递归截止：转账金额<100 USDT的交易不再追踪"""
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(anchor=tk.W, padx=5, pady=5)
        
        # 参数设置区域
        params_frame = ttk.LabelFrame(self.analysis_frame, text="分析参数")
        params_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 参数输入
        param_grid = ttk.Frame(params_frame)
        param_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(param_grid, text="时间容差(小时):").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.time_tolerance_var = tk.StringVar(value="1")
        ttk.Entry(param_grid, textvariable=self.time_tolerance_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(param_grid, text="金额容差(USDT):").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.amount_tolerance_var = tk.StringVar(value="3")
        ttk.Entry(param_grid, textvariable=self.amount_tolerance_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(param_grid, text="最少发送地址数:").grid(row=1, column=0, sticky=tk.W, padx=5)
        self.min_sender_var = tk.StringVar(value="3")
        ttk.Entry(param_grid, textvariable=self.min_sender_var, width=10).grid(row=1, column=1, padx=5)
        
        ttk.Label(param_grid, text="递归截止金额:").grid(row=1, column=2, sticky=tk.W, padx=5)
        self.min_amount_var = tk.StringVar(value="100")
        ttk.Entry(param_grid, textvariable=self.min_amount_var, width=10).grid(row=1, column=3, padx=5)
        
        # 控制按钮
        btn_frame = ttk.Frame(params_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.analyze_btn = ttk.Button(btn_frame, text="开始分析", 
                                     command=self.start_xiaozeng_analysis)
        self.analyze_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(btn_frame, text="清空结果", 
                  command=self.clear_analysis).pack(side=tk.LEFT, padx=5)
        
        # 进度显示
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(btn_frame, textvariable=self.progress_var).pack(side=tk.LEFT, padx=20)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.analysis_frame, text="分析结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.analysis_result = scrolledtext.ScrolledText(result_frame, height=20)
        self.analysis_result.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def start_xiaozeng_analysis(self):
        """开始小曾地址分析"""
        # 更新参数
        try:
            self.analyzer.time_tolerance_hours = float(self.time_tolerance_var.get())
            self.analyzer.amount_tolerance_usdt = float(self.amount_tolerance_var.get())
            self.analyzer.min_sender_count = int(self.min_sender_var.get())
            self.analyzer.min_amount_threshold = float(self.min_amount_var.get())
        except ValueError:
            messagebox.showerror("错误", "参数格式不正确，请检查输入")
            return
        
        # 禁用按钮
        self.analyze_btn.config(state='disabled')
        self.progress_var.set("分析中...")
        self.analysis_result.delete(1.0, tk.END)
        self.analysis_result.insert(tk.END, "正在分析小曾的收款地址，请稍候...\n")
        self.root.update()
        
        def analysis_thread():
            try:
                # 执行分析
                result = self.analyzer.analyze_xiaozeng_addresses()
                
                # 保存结果
                saved_files = self.analyzer.save_analysis_results(result)
                
                # 格式化输出
                output = self.format_analysis_output(result, saved_files)
                
                # 更新UI
                self.root.after(0, lambda: self.update_analysis_result(output, True))
                
            except Exception as e:
                import traceback
                error_msg = f"分析失败: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
                self.root.after(0, lambda: self.update_analysis_result(error_msg, False))
        
        threading.Thread(target=analysis_thread, daemon=True).start()
    
    def format_analysis_output(self, result: Dict, saved_files: Dict) -> str:
        """格式化分析结果输出"""
        output = f"{'='*60}\n"
        output += f"小曾收款地址分析结果\n"
        output += f"{'='*60}\n"
        output += f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 总体统计
        summary = result['summary']
        output += "## 总体统计\n"
        output += f"币安充值记录总数: {summary['total_deposit_records']}\n"
        output += f"有效发送地址数量: {summary['valid_sender_addresses']}\n"
        output += f"符合条件交易数量: {summary['total_filtered_transactions']}\n"
        output += f"真实收款地址数量: {summary['xiaozeng_real_addresses_count']}\n"
        output += f"递归追踪交易数量: {summary['recursive_trace_count']}\n\n"
        
        # 真实收款地址列表
        real_addresses = result['intersection_analysis']['xiaozeng_real_addresses']
        if real_addresses:
            output += "## 小曾真实收款地址\n"
            for i, (addr, info) in enumerate(real_addresses.items(), 1):
                output += f"{i}. {addr}\n"
                output += f"   发送地址数: {info['sender_count']}, "
                output += f"交易次数: {info['transaction_count']}, "
                output += f"总金额: {info['total_amount']:.2f} USDT\n"
            output += "\n"
        
        # 发送地址统计
        distribution = result['intersection_analysis']['statistics']['sender_count_distribution']
        if distribution:
            output += "## 发送地址数量分布\n"
            for count in sorted(distribution.keys(), reverse=True):
                freq = distribution[count]
                output += f"被 {count} 个发送地址转账: {freq} 个收款地址\n"
            output += "\n"
        
        # 保存的文件
        if saved_files and 'error' not in saved_files:
            output += "## 输出文件\n"
            file_descriptions = {
                'real_addresses': '真实收款地址列表',
                'statistics': '地址统计分析',
                'transactions': '符合条件的交易记录',
                'complete_json': '完整分析结果(JSON)'
            }
            for file_type, file_path in saved_files.items():
                desc = file_descriptions.get(file_type, file_type)
                output += f"• {desc}: {file_path}\n"
        elif 'error' in saved_files:
            output += f"⚠️ 文件保存失败: {saved_files['error']}\n"
        
        return output
    
    def update_analysis_result(self, text: str, success: bool):
        """更新分析结果显示"""
        self.analysis_result.delete(1.0, tk.END)
        self.analysis_result.insert(tk.END, text)
        
        # 恢复按钮状态
        self.analyze_btn.config(state='normal')
        self.progress_var.set("完成" if success else "失败")
        
        if success:
            messagebox.showinfo("完成", "小曾地址分析完成！结果已保存到文件。")
        else:
            messagebox.showerror("错误", "分析过程中出现错误，请查看详细信息。")
    
    def clear_analysis(self):
        """清空分析结果"""
        self.analysis_result.delete(1.0, tk.END)
        self.progress_var.set("就绪")

def main():
    """主函数"""
    root = tk.Tk()
    app = AddressTestGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
