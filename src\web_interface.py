#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面 - Flask应用
提供浏览器访问的地址测试界面
"""

from flask import Flask, render_template, request, jsonify
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from datetime import datetime
import json

app = Flask(__name__)

# 初始化工具
validator = AddressValidator()
reader = ExcelDataReader(".")
collector = AddressCollector(reader)

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/validate', methods=['POST'])
def validate_address():
    """验证地址API"""
    data = request.get_json()
    address = data.get('address', '').strip()
    
    if not address:
        return jsonify({'error': '请输入地址'})
    
    try:
        result = validator.validate_address(address)
        
        # 格式化结果
        response = {
            'address': address,
            'address_type': result['address_type'],
            'format_valid': result['format_valid'],
            'exists_on_chain': result['exists_on_chain'],
            'should_ignore': result['should_ignore'],
            'errors': result['errors'],
            'chain_info': result['chain_info'],
            'status': 'success'
        }
        
        return jsonify(response)
    
    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'})

@app.route('/api/transactions', methods=['POST'])
def get_transactions():
    """获取交易记录API"""
    data = request.get_json()
    address = data.get('address', '').strip()
    
    if not address:
        return jsonify({'error': '请输入地址'})
    
    try:
        # 先验证地址
        validation = validator.validate_address(address)
        
        if not validation['format_valid'] or validation['should_ignore']:
            return jsonify({'error': '地址无效或应忽略', 'validation': validation})
        
        # 查询交易
        transactions = []
        if validation['address_type'] == 'TRC20':
            transactions = collector.query_trc20_transaction(address, None)
        elif validation['address_type'] == 'ERC20':
            transactions = collector.query_erc20_transaction(address, None)
        
        # 格式化交易数据
        formatted_transactions = []
        for tx in transactions[:20]:  # 限制返回数量
            formatted_transactions.append({
                'hash': tx.get('tx_hash', 'N/A'),
                'from': tx.get('from_address', 'N/A'),
                'to': tx.get('to_address', 'N/A'),
                'amount': str(tx.get('amount', 'N/A')),
                'timestamp': str(tx.get('timestamp', 'N/A')),
                'block': str(tx.get('block', 'N/A'))
            })
        
        return jsonify({
            'address': address,
            'transaction_count': len(transactions),
            'transactions': formatted_transactions,
            'status': 'success'
        })
    
    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'})

@app.route('/api/batch_validate', methods=['POST'])
def batch_validate():
    """批量验证地址API"""
    data = request.get_json()
    addresses = data.get('addresses', [])
    
    if not addresses:
        return jsonify({'error': '请输入地址列表'})
    
    try:
        results = validator.validate_address_list(addresses)
        
        return jsonify({
            'total_addresses': results['total_addresses'],
            'valid_addresses': results['valid_addresses'],
            'invalid_addresses': results['invalid_addresses'],
            'ignored_addresses': results['ignored_addresses'],
            'trc20_addresses': results['trc20_addresses'],
            'erc20_addresses': results['erc20_addresses'],
            'validation_details': results['validation_details'],
            'status': 'success'
        })
    
    except Exception as e:
        return jsonify({'error': str(e), 'status': 'error'})

if __name__ == '__main__':
    print("启动Web界面...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
