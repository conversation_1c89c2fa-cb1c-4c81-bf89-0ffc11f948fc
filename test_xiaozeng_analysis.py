#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小曾地址分析功能
"""

import sys
from pathlib import Path
sys.path.append('src')

def test_data_reading():
    """测试数据读取"""
    print("测试数据读取...")
    
    from modules.data_reader import ExcelDataReader
    
    reader = ExcelDataReader(".")
    
    try:
        # 测试读取小曾币安充值记录
        deposits = reader.read_xiaozeng_binance_deposits()
        print(f"✅ 成功读取小曾币安充值记录: {len(deposits)} 条")
        print(f"   列名: {list(deposits.columns)}")
        
        if len(deposits) > 0:
            print(f"   样本数据:")
            for i, (idx, row) in enumerate(deposits.head(3).iterrows()):
                print(f"     记录{i+1}: 发送地址={row.get('发送地址')}, 金额={row.get('USDT对应数额')}, 时间={row.get('时间')}")
        
        return True
    except Exception as e:
        print(f"❌ 数据读取失败: {e}")
        return False

def test_analyzer_init():
    """测试分析器初始化"""
    print("\n测试分析器初始化...")
    
    try:
        from modules.data_reader import ExcelDataReader
        from modules.xiaozeng_address_analyzer import XiaozengAddressAnalyzer
        
        reader = ExcelDataReader(".")
        analyzer = XiaozengAddressAnalyzer(reader)
        
        print(f"✅ 分析器初始化成功")
        print(f"   时间容差: ±{analyzer.time_tolerance_hours}小时")
        print(f"   金额容差: <{analyzer.amount_tolerance_usdt}USDT")
        print(f"   最少发送地址: {analyzer.min_sender_count}个")
        print(f"   递归截止金额: {analyzer.min_amount_threshold}USDT")
        
        return True
    except Exception as e:
        print(f"❌ 分析器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deposit_extraction():
    """测试币安充值记录提取"""
    print("\n测试币安充值记录提取...")
    
    try:
        from modules.data_reader import ExcelDataReader
        from modules.xiaozeng_address_analyzer import XiaozengAddressAnalyzer
        
        reader = ExcelDataReader(".")
        analyzer = XiaozengAddressAnalyzer(reader)
        
        deposit_info = analyzer.extract_binance_deposit_info()
        print(f"✅ 成功提取币安充值记录: {len(deposit_info)} 条")
        
        if deposit_info:
            print("   前3条记录:")
            for i, info in enumerate(deposit_info[:3]):
                print(f"     {i+1}. 发送地址: {info['sender_address']}")
                print(f"        充值时间: {info['deposit_time']}")
                print(f"        USDT金额: {info['usdt_amount']}")
        
        return True
    except Exception as e:
        print(f"❌ 币安充值记录提取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("小曾地址分析功能测试")
    print("=" * 60)
    
    # 检查数据文件是否存在
    data_file = Path("xzbain.xls")
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        print("请确保 xzbain.xls 文件在当前目录中")
        return
    
    print(f"✅ 数据文件存在: {data_file}")
    
    # 运行测试
    tests = [
        test_data_reading,
        test_analyzer_init,
        test_deposit_extraction
    ]
    
    passed = 0
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"测试完成: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("✅ 所有测试通过，可以运行完整分析")
        print("\n运行完整分析:")
        print("  python src/xiaozeng_analysis.py")
        print("或启动GUI:")
        print("  python run_gui.py")
    else:
        print("❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()