#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简易分析模块
实现0.简易分析模块：计算小江和小曾币安充值记录时间交集期间，
小江共充值USDT减去小江P2P卖出(对应银行卡和微信收款)，得出小江实际USDT余额
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
try:
    from .data_reader import ExcelDataReader
except ImportError:
    from data_reader import ExcelDataReader

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleAnalyzer:
    """简易分析器"""
    
    def __init__(self, data_reader: ExcelDataReader):
        """
        初始化分析器
        
        Args:
            data_reader: Excel数据读取器
        """
        self.data_reader = data_reader
        
    def excel_date_to_datetime(self, excel_date: float) -> datetime:
        """
        将Excel日期数字转换为datetime对象
        Excel日期是从1900年1月1日开始的天数
        
        Args:
            excel_date: Excel日期数字
            
        Returns:
            datetime对象
        """
        # Excel日期基准：1900年1月1日（但Excel错误地认为1900年是闰年）
        base_date = datetime(1900, 1, 1)
        # 减去2天来修正Excel的错误
        return base_date + timedelta(days=excel_date - 2)
    
    def normalize_datetime(self, dt_value):
        """
        标准化时间值为datetime对象

        Args:
            dt_value: 时间值（可能是datetime、Timestamp或Excel数字）

        Returns:
            datetime对象
        """
        if pd.isna(dt_value):
            return None

        try:
            if isinstance(dt_value, datetime):
                return dt_value
            elif hasattr(dt_value, 'to_pydatetime'):  # pandas Timestamp
                return dt_value.to_pydatetime()
            elif isinstance(dt_value, (int, float)):
                # 检查Excel日期范围（1900-2100年）
                if 1 <= dt_value <= 73050:  # 大约对应1900-2100年
                    return self.excel_date_to_datetime(dt_value)
                else:
                    return None
            else:
                return None
        except (ValueError, OverflowError) as e:
            logger.warning(f"时间转换失败: {dt_value}, 错误: {e}")
            return None

    def find_time_intersection(self, xiajiang_records: pd.DataFrame,
                             xiaozeng_records: pd.DataFrame) -> Tuple[datetime, datetime]:
        """
        找到小江和小曾充值记录的时间交集

        Args:
            xiajiang_records: 小江充值记录
            xiaozeng_records: 小曾充值记录

        Returns:
            (交集开始时间, 交集结束时间)
        """
        # 转换时间格式，过滤无效时间
        xj_times = []
        for t in xiajiang_records['时间']:
            normalized_time = self.normalize_datetime(t)
            if normalized_time:
                xj_times.append(normalized_time)

        xz_times = []
        for t in xiaozeng_records['时间']:
            normalized_time = self.normalize_datetime(t)
            if normalized_time:
                xz_times.append(normalized_time)

        if not xj_times or not xz_times:
            raise ValueError("无法找到有效的时间数据")

        xj_start, xj_end = min(xj_times), max(xj_times)
        xz_start, xz_end = min(xz_times), max(xz_times)

        # 计算交集
        intersection_start = max(xj_start, xz_start)
        intersection_end = min(xj_end, xz_end)

        if intersection_start > intersection_end:
            raise ValueError("小江和小曾的充值时间没有交集")

        logger.info(f"小江充值时间范围: {xj_start} 到 {xj_end}")
        logger.info(f"小曾充值时间范围: {xz_start} 到 {xz_end}")
        logger.info(f"时间交集: {intersection_start} 到 {intersection_end}")

        return intersection_start, intersection_end
    
    def filter_records_by_time(self, records: pd.DataFrame, time_column: str,
                              start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        根据时间范围过滤记录

        Args:
            records: 记录DataFrame
            time_column: 时间列名
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            过滤后的记录
        """
        # 转换时间列，使用标准化函数
        records_copy = records.copy()
        records_copy['datetime'] = records_copy[time_column].apply(self.normalize_datetime)

        # 过滤掉无效时间的记录
        records_copy = records_copy.dropna(subset=['datetime'])

        # 过滤时间范围
        mask = (records_copy['datetime'] >= start_time) & (records_copy['datetime'] <= end_time)
        filtered_records = records_copy[mask]

        logger.info(f"时间过滤: 原始记录 {len(records)} 条，过滤后 {len(filtered_records)} 条")

        return filtered_records
    
    def calculate_xiajiang_deposits_in_period(self, start_time: datetime, 
                                            end_time: datetime) -> Tuple[float, pd.DataFrame]:
        """
        计算小江在指定时间段内的USDT充值总额
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            (充值总额, 充值记录)
        """
        # 读取小江币安充值记录
        xj_deposits = self.data_reader.read_xiaojiang_binance_deposits()
        
        # 过滤时间范围
        filtered_deposits = self.filter_records_by_time(
            xj_deposits, '时间', start_time, end_time
        )
        
        # 计算总额
        total_deposits = filtered_deposits['USDT对应数额'].sum()
        
        logger.info(f"小江在交集期间充值USDT总额: {total_deposits}")
        
        return total_deposits, filtered_deposits
    
    def calculate_xiajiang_p2p_sales_in_period(self, start_time: datetime, 
                                             end_time: datetime) -> Tuple[float, pd.DataFrame]:
        """
        计算小江在指定时间段内的P2P卖出总额
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            (P2P卖出总额, P2P记录)
        """
        # 读取小江P2P卖出记录
        xj_p2p_sales = self.data_reader.read_xiaojiang_p2p_sales()
        
        # 过滤时间范围（使用创建时间）
        filtered_sales = self.filter_records_by_time(
            xj_p2p_sales, '创建时间', start_time, end_time
        )
        
        # 只统计已完成的交易
        completed_sales = filtered_sales[filtered_sales['状态'] == 'Completed']
        
        # 计算总额
        total_sales = completed_sales['数额'].sum()
        
        logger.info(f"小江在交集期间P2P卖出USDT总额: {total_sales}")
        
        return total_sales, completed_sales
    
    def find_matching_fiat_receipts(self, p2p_sales: pd.DataFrame, 
                                   time_window_hours: int = 48) -> Tuple[float, List[Dict]]:
        """
        查找与P2P卖出对应的法币收款记录
        
        Args:
            p2p_sales: P2P卖出记录
            time_window_hours: 时间窗口（小时）
            
        Returns:
            (匹配的法币金额, 匹配证据链)
        """
        # 读取小江银行和微信收款记录
        xj_bank_records = self.data_reader.read_xiaojiang_bank_records()
        xj_wechat_records = self.data_reader.read_xiaojiang_wechat_receipts()
        
        matched_amount = 0.0
        evidence_chain = []
        
        for _, p2p_sale in p2p_sales.iterrows():
            # 安全转换时间
            p2p_time = self.normalize_datetime(p2p_sale['支付时间'])
            if not p2p_time:
                continue

            p2p_amount_cny = p2p_sale['总金额']
            payment_method = p2p_sale['支付方式']
            
            # 定义时间窗口
            window_start = p2p_time - timedelta(hours=time_window_hours)
            window_end = p2p_time + timedelta(hours=time_window_hours)
            
            matched_receipt = None
            
            # 根据支付方式选择对应的收款记录
            if payment_method in ['WECHAT', 'WeChat']:
                # 查找微信收款记录
                for _, wechat_record in xj_wechat_records.iterrows():
                    receipt_time = self.normalize_datetime(wechat_record['交易时间'])
                    if not receipt_time:
                        continue

                    receipt_amount_fen = wechat_record['交易金额(分)']
                    receipt_amount_yuan = receipt_amount_fen / 100  # 分转元
                    
                    # 检查时间和金额匹配
                    if (window_start <= receipt_time <= window_end and 
                        abs(receipt_amount_yuan - p2p_amount_cny) / p2p_amount_cny < 0.05):  # 5%误差容忍
                        matched_receipt = {
                            'type': 'wechat',
                            'time': receipt_time,
                            'amount': receipt_amount_yuan,
                            'record': wechat_record
                        }
                        break
            
            elif payment_method in ['ALIPAY', 'BANK']:
                # 查找银行收款记录
                for _, bank_record in xj_bank_records.iterrows():
                    receipt_time = self.normalize_datetime(bank_record['交易时间'])
                    if not receipt_time:
                        continue

                    receipt_amount = bank_record['交易金额']
                    
                    # 检查时间和金额匹配
                    if (window_start <= receipt_time <= window_end and 
                        abs(receipt_amount - p2p_amount_cny) / p2p_amount_cny < 0.05):  # 5%误差容忍
                        matched_receipt = {
                            'type': 'bank',
                            'time': receipt_time,
                            'amount': receipt_amount,
                            'record': bank_record
                        }
                        break
            
            # 如果找到匹配的收款记录
            if matched_receipt:
                matched_amount += p2p_sale['数额']  # 累加USDT数额
                evidence_chain.append({
                    'p2p_sale': p2p_sale,
                    'fiat_receipt': matched_receipt,
                    'usdt_amount': p2p_sale['数额'],
                    'cny_amount': p2p_amount_cny,
                    'p2p_time': p2p_time,
                    'receipt_time': matched_receipt['time'],
                    'payment_method': payment_method,
                    'time_diff_hours': abs((matched_receipt['time'] - p2p_time).total_seconds() / 3600)
                })
        
        logger.info(f"找到匹配的法币收款记录 {len(evidence_chain)} 条，对应USDT: {matched_amount}")
        
        return matched_amount, evidence_chain
    
    def run_simple_analysis(self) -> Dict:
        """
        运行简易分析
        
        Returns:
            分析结果字典
        """
        logger.info("开始执行简易分析...")
        
        # 1. 读取小江和小曾的币安充值记录
        xj_deposits = self.data_reader.read_xiaojiang_binance_deposits()
        xz_deposits = self.data_reader.read_xiaozeng_binance_deposits()
        
        # 2. 找到时间交集
        intersection_start, intersection_end = self.find_time_intersection(xj_deposits, xz_deposits)
        
        # 3. 计算小江在交集期间的充值总额
        xj_total_deposits, xj_deposit_records = self.calculate_xiajiang_deposits_in_period(
            intersection_start, intersection_end
        )
        
        # 4. 计算小江在交集期间的P2P卖出总额
        xj_total_p2p_sales, xj_p2p_records = self.calculate_xiajiang_p2p_sales_in_period(
            intersection_start, intersection_end
        )
        
        # 5. 查找与P2P卖出对应的法币收款记录
        matched_fiat_amount, evidence_chain = self.find_matching_fiat_receipts(xj_p2p_records)

        # 6. 计算法币收款总金额
        total_fiat_received = sum(evidence['cny_amount'] for evidence in evidence_chain)

        # 7. 计算小江实际USDT余额（可能流向小曾的嫌疑金额）
        suspected_amount = xj_total_deposits - matched_fiat_amount
        
        # 8. 生成分析结果
        result = {
            'analysis_period': {
                'start': intersection_start,
                'end': intersection_end,
                'duration_days': (intersection_end - intersection_start).days
            },
            'xiajiang_deposits': {
                'total_usdt': xj_total_deposits,
                'record_count': len(xj_deposit_records),
                'records': xj_deposit_records
            },
            'xiajiang_p2p_sales': {
                'total_usdt': xj_total_p2p_sales,
                'record_count': len(xj_p2p_records),
                'records': xj_p2p_records
            },
            'matched_fiat_receipts': {
                'matched_usdt': matched_fiat_amount,
                'total_fiat_cny': total_fiat_received,
                'evidence_count': len(evidence_chain),
                'evidence_chain': evidence_chain,
                'average_exchange_rate': (total_fiat_received / matched_fiat_amount) if matched_fiat_amount > 0 else 0
            },
            'final_calculation': {
                'total_deposits_usdt': xj_total_deposits,
                'total_p2p_sales_usdt': xj_total_p2p_sales,
                'excluded_usdt_with_fiat_proof': matched_fiat_amount,
                'total_fiat_received_cny': total_fiat_received,
                'suspected_flow_to_xiaozeng_usdt': suspected_amount,
                'exclusion_rate': (matched_fiat_amount / xj_total_deposits * 100) if xj_total_deposits > 0 else 0,
                'p2p_coverage_rate': (matched_fiat_amount / xj_total_p2p_sales * 100) if xj_total_p2p_sales > 0 else 0
            }
        }
        
        logger.info("简易分析完成")
        logger.info(f"分析期间: {intersection_start} 到 {intersection_end}")
        logger.info(f"小江充值USDT总额: {xj_total_deposits}")
        logger.info(f"小江P2P卖出USDT总额: {xj_total_p2p_sales}")
        logger.info(f"可排除USDT金额: {matched_fiat_amount}")
        logger.info(f"法币收款总金额: {total_fiat_received:.2f} CNY")
        logger.info(f"平均汇率: {result['matched_fiat_receipts']['average_exchange_rate']:.2f} CNY/USDT")
        logger.info(f"嫌疑流向小曾的USDT: {suspected_amount}")
        logger.info(f"排除率: {result['final_calculation']['exclusion_rate']:.2f}%")
        logger.info(f"P2P覆盖率: {result['final_calculation']['p2p_coverage_rate']:.2f}%")
        
        return result

def main():
    """测试函数"""
    # 创建数据读取器和分析器
    reader = ExcelDataReader()
    analyzer = SimpleAnalyzer(reader)
    
    # 运行简易分析
    result = analyzer.run_simple_analysis()
    
    print("\n" + "=" * 80)
    print("简易分析结果报告")
    print("=" * 80)
    
    print(f"\n分析时间段: {result['analysis_period']['start']} 到 {result['analysis_period']['end']}")
    print(f"分析天数: {result['analysis_period']['duration_days']} 天")
    
    print(f"\n小江币安充值情况:")
    print(f"  - 充值记录数: {result['xiajiang_deposits']['record_count']} 条")
    print(f"  - 充值USDT总额: {result['xiajiang_deposits']['total_usdt']:.2f}")
    
    print(f"\n小江P2P卖出情况:")
    print(f"  - P2P卖出记录数: {result['xiajiang_p2p_sales']['record_count']} 条")
    print(f"  - P2P卖出USDT总额: {result['xiajiang_p2p_sales']['total_usdt']:.2f}")
    
    print(f"\n法币收款匹配情况:")
    print(f"  - 匹配证据链数: {result['matched_fiat_receipts']['evidence_count']} 条")
    print(f"  - 可排除USDT金额: {result['matched_fiat_receipts']['matched_usdt']:.2f}")
    
    print(f"\n最终计算结果:")
    print(f"  - 小江充值USDT总额: {result['final_calculation']['total_deposits_usdt']:.2f}")
    print(f"  - 可排除USDT金额: {result['final_calculation']['excluded_usdt_with_fiat_proof']:.2f}")
    print(f"  - 嫌疑流向小曾的USDT: {result['final_calculation']['suspected_flow_to_xiaozeng_usdt']:.2f}")
    print(f"  - 排除率: {result['final_calculation']['exclusion_rate']:.2f}%")

if __name__ == "__main__":
    main()
