#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MoneyTrace GUI启动脚本
"""

import sys
from pathlib import Path

# 添加src目录到路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from gui_tkinter import main
    
    if __name__ == "__main__":
        print("启动MoneyTrace USDT交易查询工具...")
        main()
        
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保所有依赖模块都已正确安装")
    sys.exit(1)
except Exception as e:
    print(f"启动失败: {e}")
    sys.exit(1)