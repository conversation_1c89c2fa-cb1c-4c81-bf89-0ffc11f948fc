#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向追踪模块测试脚本
基于multitrace.md需求测试资金流向追踪功能
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.fund_flow_tracker import FundFlowTracker, TrackingConfig
from datetime import datetime

def test_basic_tracking():
    """基础追踪测试"""
    print("=" * 80)
    print("MoneyTrace 资金流向追踪模块 - 基础测试")
    print("=" * 80)

    # 创建测试配置
    config = TrackingConfig(
        time_window_months=2,
        max_levels=3,  # 减少层级用于测试
        min_amount_ratio=0.125,  # 1/8
        min_amount_filter_ratio=0.25,  # 1/4
        api_delay=1.0,  # 减少延迟用于测试
        max_transactions_per_level=3
    )

    # 创建追踪器
    tracker = FundFlowTracker(config)

    # 测试地址（小江的第一个地址）
    test_address = "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew"

    print(f"测试地址: {test_address}")
    print("根据multitrace.md需求:")
    print("1. 获取该地址转出的所有USDT交易，先取第一条")
    print("2. 记录交易hash、金额m、时间t、对方地址b")
    print("3. 对地址b查询t后2个月内转出的USDT交易")
    print("4. 只记录金额在 m/4 < amount <= m+1 范围内的交易")
    print("5. 递归追踪直到达到终止条件")
    print()

    # 执行追踪
    result = tracker.track_fund_flow(test_address)

    # 显示结果
    print("\n" + "=" * 80)
    print("测试结果")
    print("=" * 80)

    if result['success']:
        print("✅ 追踪成功完成！")

        summary = result['summary']
        print(f"\n📊 追踪摘要:")
        print(f"  总交易数: {summary['total_transactions']}")
        print(f"  总金额: {summary['total_amount']:.6f} USDT")
        print(f"  涉及地址数: {summary['unique_addresses']}")
        print(f"  最大层级: {summary['max_level_reached']}")
        print(f"  地址链条数: {summary['address_chains_count']}")

        if summary['total_transactions'] > 0:
            print(f"\n📋 交易详情:")
            for i, tx in enumerate(result['transactions'][:5], 1):  # 只显示前5条
                print(f"  交易{i} (第{tx['level']}层):")
                print(f"    哈希: {tx['hash'][:20]}...")
                print(f"    金额: {tx['amount']:.6f} USDT")
                print(f"    发送方: {tx['from_address'][:20]}...")
                print(f"    接收方: {tx['to_address'][:20]}...")
                print(f"    时间: {tx['timestamp']}")
                print()

            if len(result['transactions']) > 5:
                print(f"  ... 还有 {len(result['transactions']) - 5} 条交易")

        # 保存结果
        saved_file = tracker.save_tracking_result(result)
        print(f"\n💾 详细结果已保存到: {saved_file}")

    else:
        print("❌ 追踪失败")
        print(f"错误: {result['error']}")

        if result.get('validation'):
            validation = result['validation']
            print(f"\n地址验证结果:")
            print(f"  格式有效: {validation['format_valid']}")
            print(f"  链上存在: {validation['exists_on_chain']}")
            print(f"  应忽略: {validation['should_ignore']}")

    return result

def main():
    """主函数"""
    print("MoneyTrace 资金流向追踪模块测试")
    print("基于multitrace.md需求实现")
    print("=" * 80)

    try:
        # 基础测试
        print("🚀 开始基础测试...")
        basic_result = test_basic_tracking()

        print("\n" + "=" * 80)
        print("测试完成！")
        print("=" * 80)

        if basic_result['success']:
            print("✅ 资金流向追踪模块工作正常")
            print("📁 详细结果已保存到 data/output 目录")
        else:
            print("⚠️ 测试未完全成功，请检查网络连接和API访问")

    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()