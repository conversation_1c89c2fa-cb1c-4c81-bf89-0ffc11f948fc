#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置管理
管理区块链API的配置信息，包括API密钥（可选）
"""

import os
from typing import Optional

class APIConfig:
    """API配置管理器"""
    
    def __init__(self):
        """初始化配置"""
        # Tronscan API配置（免费，无需API Key）
        self.tron_api_base = "https://apilist.tronscanapi.com/api"
        self.tron_api_key = None  # Tronscan免费API无需密钥
        
        # Etherscan API配置
        self.eth_api_base = "https://api.etherscan.io/api"
        self.eth_api_key = self._get_etherscan_api_key()
        
        # 请求配置
        self.request_delay = 0.3  # 基础延迟
        self.max_retries = 3
        self.timeout = 15
        
    def _get_etherscan_api_key(self) -> Optional[str]:
        """
        获取Etherscan API密钥
        
        Returns:
            API密钥或None
        """
        # 优先从环境变量获取
        api_key = os.getenv('ETHERSCAN_API_KEY')
        if api_key:
            return api_key
        
        # 从配置文件获取（如果存在）
        try:
            from pathlib import Path
            config_file = Path(__file__).parent / 'etherscan_api_key.txt'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    api_key = f.read().strip()
                    if api_key:
                        return api_key
        except Exception:
            pass
        
        return None
    
    def get_tron_api_url(self, endpoint: str) -> str:
        """
        获取Tron API URL
        
        Args:
            endpoint: API端点
            
        Returns:
            完整的API URL
        """
        return f"{self.tron_api_base}/{endpoint}"
    
    def get_eth_api_params(self, base_params: dict) -> dict:
        """
        获取Ethereum API参数
        
        Args:
            base_params: 基础参数
            
        Returns:
            包含API密钥的完整参数
        """
        params = base_params.copy()
        if self.eth_api_key:
            params['apikey'] = self.eth_api_key
        return params
    
    def get_request_delay(self, api_type: str) -> float:
        """
        获取请求延迟时间
        
        Args:
            api_type: API类型 ('tron' 或 'eth')
            
        Returns:
            延迟时间（秒）
        """
        if api_type == 'eth' and not self.eth_api_key:
            # 没有API密钥时增加延迟
            return self.request_delay * 3
        return self.request_delay
    
    def has_eth_api_key(self) -> bool:
        """检查是否有Etherscan API密钥"""
        return self.eth_api_key is not None
    
    def get_api_status(self) -> dict:
        """
        获取API状态信息
        
        Returns:
            API状态字典
        """
        return {
            'tron_api': {
                'base_url': self.tron_api_base,
                'has_api_key': False,
                'is_free': True,
                'rate_limit': '免费版本有频率限制'
            },
            'eth_api': {
                'base_url': self.eth_api_base,
                'has_api_key': self.has_eth_api_key(),
                'is_free': not self.has_eth_api_key(),
                'rate_limit': '免费版本: 5次/秒, 100,000次/天' if not self.has_eth_api_key() else '付费版本: 更高频率限制'
            }
        }

# 全局配置实例
api_config = APIConfig()

def print_api_status():
    """打印API状态信息"""
    status = api_config.get_api_status()
    
    print("=" * 60)
    print("区块链API配置状态")
    print("=" * 60)
    
    print("\n🔗 Tron API (Tronscan):")
    print(f"   URL: {status['tron_api']['base_url']}")
    print(f"   免费使用: {status['tron_api']['is_free']}")
    print(f"   频率限制: {status['tron_api']['rate_limit']}")
    
    print("\n🔗 Ethereum API (Etherscan):")
    print(f"   URL: {status['eth_api']['base_url']}")
    print(f"   有API密钥: {status['eth_api']['has_api_key']}")
    print(f"   免费使用: {status['eth_api']['is_free']}")
    print(f"   频率限制: {status['eth_api']['rate_limit']}")
    
    if not status['eth_api']['has_api_key']:
        print("\n💡 提示:")
        print("   - Etherscan免费API有频率限制，可能影响查询速度")
        print("   - 如需提高查询速度，可申请免费API密钥:")
        print("     1. 访问 https://etherscan.io/apis")
        print("     2. 注册账户并申请免费API密钥")
        print("     3. 设置环境变量: ETHERSCAN_API_KEY=你的密钥")
        print("     4. 或创建文件: src/config/etherscan_api_key.txt")

def main():
    """测试函数"""
    print_api_status()

if __name__ == "__main__":
    main()
