#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量查询小曾交易记录 - 实现readme任务2
通过链上查询获取小曾所有币安充值收款地址
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.data_reader import ExcelDataReader
from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
import pandas as pd
from datetime import datetime
import time

class XiaozengAnalyzer:
    """小曾交易记录分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.reader = ExcelDataReader(".")
        self.validator = AddressValidator()
        self.collector = AddressCollector(self.reader)
        
    def read_xiaozeng_deposits(self):
        """读取小曾币安充值记录"""
        try:
            print("正在读取小曾币安充值记录...")
            deposits = self.reader.read_xiaozeng_binance_deposits()
            print(f"成功读取 {len(deposits)} 条充值记录")
            
            # 显示数据结构
            print("\n数据列名:")
            for i, col in enumerate(deposits.columns):
                print(f"  {i}: {col}")
            
            # 显示前几条记录
            print(f"\n前5条记录:")
            print(deposits.head())
            
            return deposits
            
        except Exception as e:
            print(f"读取充值记录失败: {e}")
            return None
    
    def extract_sender_addresses(self, deposits):
        """从充值记录中提取发送地址"""
        print("\n开始提取发送地址...")
        
        sender_addresses = []
        address_info = []
        
        for index, record in deposits.iterrows():
            sender_addr = str(record['发送地址']).strip()
            usdt_amount = record['USDT对应数额']
            deposit_time = record['时间']
            
            # 过滤掉纯数字ID（这些不是区块链地址）
            if sender_addr.isdigit():
                print(f"跳过数字ID: {sender_addr}")
                continue
            
            # 验证地址格式
            if len(sender_addr) >= 30:  # 区块链地址通常比较长
                sender_addresses.append(sender_addr)
                address_info.append({
                    'address': sender_addr,
                    'amount': usdt_amount,
                    'time': deposit_time,
                    'record_index': index
                })
                print(f"提取地址: {sender_addr} (金额: {usdt_amount} USDT, 时间: {deposit_time})")
        
        print(f"\n总共提取到 {len(sender_addresses)} 个有效地址")
        return sender_addresses, address_info
    
    def validate_addresses(self, addresses):
        """批量验证地址有效性"""
        print(f"\n开始验证 {len(addresses)} 个地址...")
        
        valid_addresses = []
        invalid_addresses = []
        
        for i, addr in enumerate(addresses, 1):
            print(f"验证地址 {i}/{len(addresses)}: {addr}")
            
            try:
                result = self.validator.validate_address(addr)
                
                if result['format_valid'] and result['exists_on_chain']:
                    valid_addresses.append(addr)
                    print(f"  ✓ 有效 ({result['address_type']})")
                else:
                    invalid_addresses.append(addr)
                    print(f"  ✗ 无效 ({', '.join(result['errors'])})")
                    
                # 避免API频率限制
                time.sleep(0.5)
                
            except Exception as e:
                invalid_addresses.append(addr)
                print(f"  ✗ 验证失败: {e}")
        
        print(f"\n地址验证完成:")
        print(f"  有效地址: {len(valid_addresses)}")
        print(f"  无效地址: {len(invalid_addresses)}")
        
        return valid_addresses, invalid_addresses
    
    def query_sender_transactions(self, addresses, address_info):
        """查询发送地址的USDT交易，获取小曾的收款地址"""
        print(f"\n开始查询 {len(addresses)} 个地址的USDT交易...")
        
        xiaozeng_receive_addresses = set()
        transaction_records = []
        
        for i, addr in enumerate(addresses, 1):
            print(f"\n查询地址 {i}/{len(addresses)}: {addr}")
            
            try:
                # 获取该地址的信息
                addr_info = next((info for info in address_info if info['address'] == addr), None)
                
                # 查询USDT交易
                transactions = self.collector.query_trc20_usdt_transactions(addr, limit=50)
                
                print(f"  找到 {len(transactions)} 条USDT交易")
                
                # 分析交易，找出接收地址
                for tx in transactions:
                    from_addr = tx.get('from_address', '')
                    to_addr = tx.get('to_address', '')
                    amount = tx.get('amount_usdt', 0)
                    tx_time = tx.get('timestamp', '')
                    
                    # 如果这个地址是发送方，那么接收方就是小曾的收款地址
                    if from_addr.lower() == addr.lower():
                        xiaozeng_receive_addresses.add(to_addr)
                        transaction_records.append({
                            'sender_address': from_addr,
                            'xiaozeng_receive_address': to_addr,
                            'amount_usdt': amount,
                            'tx_hash': tx.get('tx_hash', ''),
                            'timestamp': tx_time,
                            'deposit_amount': addr_info['amount'] if addr_info else 0,
                            'deposit_time': addr_info['time'] if addr_info else None
                        })
                        print(f"    发现收款地址: {to_addr} (金额: {amount} USDT)")
                
                # 避免API频率限制
                time.sleep(1)
                
            except Exception as e:
                print(f"  查询失败: {e}")
        
        print(f"\n交易查询完成:")
        print(f"  发现小曾收款地址: {len(xiaozeng_receive_addresses)} 个")
        print(f"  相关交易记录: {len(transaction_records)} 条")
        
        return list(xiaozeng_receive_addresses), transaction_records
    
    def save_results(self, receive_addresses, transaction_records):
        """保存分析结果"""
        print(f"\n保存分析结果...")
        
        # 保存收款地址
        with open('xiaozeng_receive_addresses_final.txt', 'w', encoding='utf-8') as f:
            f.write("# 小曾币安充值收款地址（并集）\n")
            f.write(f"# 生成时间: {datetime.now()}\n")
            f.write(f"# 总计: {len(receive_addresses)} 个地址\n\n")
            
            for addr in receive_addresses:
                f.write(f"{addr}\n")
        
        print(f"  收款地址已保存到: xiaozeng_receive_addresses_final.txt")
        
        # 保存交易记录
        if transaction_records:
            df = pd.DataFrame(transaction_records)
            df.to_csv('xiaozeng_transaction_records.csv', index=False, encoding='utf-8-sig')
            print(f"  交易记录已保存到: xiaozeng_transaction_records.csv")
    
    def analyze_xiaozeng_deposits(self):
        """分析小曾的充值记录，获取发送地址"""
        print("="*60)
        print("小曾收款地址获取分析 - 任务2")
        print("="*60)
        
        # 1. 读取充值记录
        deposits = self.read_xiaozeng_deposits()
        if deposits is None:
            return
        
        # 2. 提取发送地址
        sender_addresses, address_info = self.extract_sender_addresses(deposits)
        if not sender_addresses:
            print("未找到有效的发送地址")
            return
        
        # 3. 验证地址有效性
        valid_addresses, invalid_addresses = self.validate_addresses(sender_addresses)
        if not valid_addresses:
            print("没有有效的地址可以查询")
            return
        
        # 4. 查询交易记录，获取小曾的收款地址
        receive_addresses, transaction_records = self.query_sender_transactions(valid_addresses, address_info)
        
        # 5. 保存结果
        if receive_addresses:
            self.save_results(receive_addresses, transaction_records)
        
        print(f"\n{'='*60}")
        print("分析完成！")
        print(f"{'='*60}")
        print(f"发送地址总数: {len(sender_addresses)}")
        print(f"有效发送地址: {len(valid_addresses)}")
        print(f"小曾收款地址: {len(receive_addresses)}")
        print(f"相关交易记录: {len(transaction_records)}")
        print(f"{'='*60}")

def main():
    """主函数"""
    analyzer = XiaozengAnalyzer()
    analyzer.analyze_xiaozeng_deposits()

if __name__ == "__main__":
    main()