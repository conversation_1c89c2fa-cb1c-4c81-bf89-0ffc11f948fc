#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量查询小曾交易记录 - 实现readme任务2
通过链上查询获取小曾所有币安充值收款地址

测试地址以及交易：
96215380	USDT	3773.00000000 Spot Wallet	3773	TSPWd3iExhAMwfB5c7eWb49dMFHMpKegB6	2023/10/3 2:28																				
96215380	USDT	1362.00000000 Spot Wallet	1362	TLWWmTN7rRU4SbEdMRpMq6xhM4afRM9gSj	2023/9/22 10:50																				


"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.data_reader import ExcelDataReader
from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
import pandas as pd
from datetime import datetime, timedelta
import time

class XiaozengAnalyzer:
    """小曾交易记录分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.reader = ExcelDataReader(".")
        self.validator = AddressValidator()
        self.collector = AddressCollector(self.reader)
        
    def read_xiaozeng_deposits(self):
        """读取小曾币安充值记录"""
        try:
            print("正在读取小曾币安充值记录...")
            deposits = self.reader.read_xiaozeng_binance_deposits()
            print(f"成功读取 {len(deposits)} 条充值记录")
            
            # 显示数据结构
            print("\n数据列名:")
            for i, col in enumerate(deposits.columns):
                print(f"  {i}: {col}")
            
            # 显示前几条记录
            print(f"\n前5条记录:")
            print(deposits.head())
            
            return deposits
            
        except Exception as e:
            print(f"读取充值记录失败: {e}")
            return None
    
    def extract_sender_addresses(self, deposits):
        """从充值记录中提取发送地址"""
        print("\n开始提取发送地址...")
        
        sender_addresses = []
        address_info = []
        
        for index, record in deposits.iterrows():
            sender_addr = str(record['发送地址']).strip()
            usdt_amount = record['USDT对应数额']
            deposit_time = record['时间']
            
            # 过滤掉纯数字ID（这些不是区块链地址）
            if sender_addr.isdigit():
                print(f"跳过数字ID: {sender_addr}")
                continue
            
            # 验证地址格式
            if len(sender_addr) >= 30:  # 区块链地址通常比较长
                sender_addresses.append(sender_addr)
                address_info.append({
                    'address': sender_addr,
                    'amount': usdt_amount,
                    'time': deposit_time,
                    'record_index': index
                })
                print(f"提取地址: {sender_addr} (金额: {usdt_amount} USDT, 时间: {deposit_time})")
        
        print(f"\n总共提取到 {len(sender_addresses)} 个有效地址")
        return sender_addresses, address_info
    
    def validate_addresses(self, addresses):
        """批量验证地址有效性"""
        print(f"\n开始验证 {len(addresses)} 个地址...")
        
        valid_addresses = []
        invalid_addresses = []
        
        for i, addr in enumerate(addresses, 1):
            print(f"验证地址 {i}/{len(addresses)}: {addr}")
            
            try:
                result = self.validator.validate_address(addr)
                
                if result['format_valid'] and result['exists_on_chain']:
                    valid_addresses.append(addr)
                    print(f"  ✓ 有效 ({result['address_type']})")
                else:
                    invalid_addresses.append(addr)
                    print(f"  ✗ 无效 ({', '.join(result['errors'])})")
                    
                # 避免API频率限制
                time.sleep(0.5)
                
            except Exception as e:
                invalid_addresses.append(addr)
                print(f"  ✗ 验证失败: {e}")
        
        print(f"\n地址验证完成:")
        print(f"  有效地址: {len(valid_addresses)}")
        print(f"  无效地址: {len(invalid_addresses)}")
        
        return valid_addresses, invalid_addresses
    
    def query_sender_transactions_with_time_filter(self, addresses, address_info):
        """查询发送地址的USDT交易，根据时间和金额筛选小曾的真实收款地址"""
        print(f"\n开始查询 {len(addresses)} 个地址的USDT交易...")
        print("筛选条件: 时间匹配±1小时，金额误差<3 USDT")
        
        # 用于统计每个收款地址被多少个发送地址转账过
        receive_address_count = {}
        transaction_records = []
        
        for i, addr in enumerate(addresses, 1):
            print(f"\n查询地址 {i}/{len(addresses)}: {addr}")
            
            try:
                # 获取该地址对应的充值信息
                addr_info = next((info for info in address_info if info['address'] == addr), None)
                if not addr_info:
                    print(f"  跳过：未找到对应的充值记录")
                    continue
                
                deposit_time = addr_info['time']
                deposit_amount = addr_info['amount']
                
                print(f"  充值时间: {deposit_time}")
                print(f"  充值金额: {deposit_amount} USDT")
                
                # 计算时间范围（±1小时）
                start_time = deposit_time - timedelta(hours=1)
                end_time = deposit_time + timedelta(hours=1)
                
                # 查询指定时间范围内的USDT交易
                transactions = self.collector.query_trc20_usdt_transactions_with_time_filter(
                    addr, start_time, end_time, limit=20)
                print(f"  找到 {len(transactions)} 条USDT交易")
                
                # 筛选符合条件的交易
                matched_transactions = []
                for tx in transactions:
                    from_addr = tx.get('from_address', '')
                    to_addr = tx.get('to_address', '')
                    amount = tx.get('amount_usdt', 0)
                    tx_time = tx.get('timestamp', '')
                    
                    # 只处理该地址作为发送方的交易
                    if from_addr.lower() != addr.lower():
                        continue
                    
                    # 时间筛选：±1小时
                    time_diff = None
                    if isinstance(tx_time, datetime) and isinstance(deposit_time, datetime):
                        time_diff = abs((tx_time - deposit_time).total_seconds())
                        if time_diff > 3600:  # 1小时 = 3600秒
                            continue
                    else:
                        continue
                    
                    # 金额匹配：误差<3 USDT
                    amount_diff = abs(amount - deposit_amount)
                    if amount_diff >= 3:
                        continue
                    
                    matched_transactions.append({
                        'sender_address': from_addr,
                        'receive_address': to_addr,
                        'amount_usdt': amount,
                        'tx_hash': tx.get('tx_hash', ''),
                        'tx_timestamp': tx_time,
                        'deposit_amount': deposit_amount,
                        'deposit_time': deposit_time,
                        'time_diff_seconds': time_diff if isinstance(tx_time, datetime) and isinstance(deposit_time, datetime) else None
                    })
                    
                    # 统计收款地址出现次数
                    if to_addr not in receive_address_count:
                        receive_address_count[to_addr] = {
                            'count': 0,
                            'sender_addresses': set(),
                            'total_amount': 0,
                            'transactions': []
                        }
                    
                    receive_address_count[to_addr]['count'] += 1
                    receive_address_count[to_addr]['sender_addresses'].add(from_addr)
                    receive_address_count[to_addr]['total_amount'] += amount
                    receive_address_count[to_addr]['transactions'].append(matched_transactions[-1])
                    
                    print(f"    ✓ 匹配: {to_addr} ({amount} USDT, 时间差{time_diff/3600:.1f}h, 金额差{amount_diff:.1f})")
                
                transaction_records.extend(matched_transactions)
                print(f"  符合条件的交易: {len(matched_transactions)} 条")
                
                # 避免API频率限制
                time.sleep(1)
                
            except Exception as e:
                print(f"  查询失败: {e}")
        
        # 分析交集：找出被多个发送地址转账的收款地址
        print(f"\n📊 收款地址统计分析:")
        print(f"总共发现 {len(receive_address_count)} 个收款地址")
        
        # 按发送地址数量排序
        sorted_addresses = sorted(receive_address_count.items(), 
                                key=lambda x: len(x[1]['sender_addresses']), 
                                reverse=True)
        
        xiaozeng_real_addresses = []
        
        print(f"\n收款地址详细统计:")
        for receive_addr, stats in sorted_addresses:
            sender_count = len(stats['sender_addresses'])
            total_amount = stats['total_amount']
            transaction_count = stats['count']
            
            print(f"\n地址: {receive_addr}")
            print(f"  被 {sender_count} 个不同发送地址转账")
            print(f"  总交易次数: {transaction_count}")
            print(f"  总金额: {total_amount:.2f} USDT")
            print(f"  发送地址: {', '.join(list(stats['sender_addresses'])[:3])}{'...' if sender_count > 3 else ''}")
            
            # 交集逻辑：被3个或以上不同发送地址转账的才认为是小曾的真实收款地址
            if sender_count >= 3:
                xiaozeng_real_addresses.append(receive_addr)
                print(f"  ✅ 认定为小曾真实收款地址")
            else:
                print(f"  ❌ 发送地址数量不足，不认定为真实收款地址")
        
        print(f"\n🎯 最终结果:")
        print(f"  小曾真实收款地址: {len(xiaozeng_real_addresses)} 个")
        print(f"  符合条件的交易记录: {len(transaction_records)} 条")
        
        return xiaozeng_real_addresses, transaction_records, receive_address_count
    
    def save_results(self, receive_addresses, transaction_records, address_stats):
        """保存分析结果"""
        print(f"\n保存分析结果...")
        
        # 保存小曾真实收款地址
        with open('xiaozeng_real_receive_addresses.txt', 'w', encoding='utf-8') as f:
            f.write("# 小曾真实币安充值收款地址（交集分析结果）\n")
            f.write(f"# 生成时间: {datetime.now()}\n")
            f.write(f"# 筛选条件: 时间匹配±1小时，金额误差<3 USDT，被≥3个发送地址转账\n")
            f.write(f"# 总计: {len(receive_addresses)} 个地址\n\n")
            
            for addr in receive_addresses:
                stats = address_stats[addr]
                f.write(f"{addr}\n")
                f.write(f"  # 被 {len(stats['sender_addresses'])} 个发送地址转账，总金额: {stats['total_amount']:.2f} USDT\n\n")
        
        print(f"  真实收款地址已保存到: xiaozeng_real_receive_addresses.txt")
        
        # 保存详细统计信息
        with open('xiaozeng_address_statistics.txt', 'w', encoding='utf-8') as f:
            f.write("# 小曾收款地址统计分析\n")
            f.write(f"# 生成时间: {datetime.now()}\n\n")
            
            sorted_addresses = sorted(address_stats.items(), 
                                    key=lambda x: len(x[1]['sender_addresses']), 
                                    reverse=True)
            
            for receive_addr, stats in sorted_addresses:
                sender_count = len(stats['sender_addresses'])
                f.write(f"地址: {receive_addr}\n")
                f.write(f"  发送地址数量: {sender_count}\n")
                f.write(f"  交易次数: {stats['count']}\n")
                f.write(f"  总金额: {stats['total_amount']:.2f} USDT\n")
                f.write(f"  是否为真实地址: {'是' if sender_count >= 3 else '否'}\n")
                f.write(f"  发送地址列表: {', '.join(stats['sender_addresses'])}\n\n")
        
        print(f"  统计信息已保存到: xiaozeng_address_statistics.txt")
        
        # 保存交易记录
        if transaction_records:
            df = pd.DataFrame(transaction_records)
            df.to_csv('xiaozeng_filtered_transactions.csv', index=False, encoding='utf-8-sig')
            print(f"  筛选后的交易记录已保存到: xiaozeng_filtered_transactions.csv")
    
    def analyze_xiaozeng_deposits(self):
        """分析小曾的充值记录，获取发送地址"""
        print("="*60)
        print("小曾收款地址获取分析 - 任务2")
        print("="*60)
        
        # 1. 读取充值记录
        deposits = self.read_xiaozeng_deposits()
        if deposits is None:
            return
        
        # 2. 提取发送地址
        sender_addresses, address_info = self.extract_sender_addresses(deposits)
        if not sender_addresses:
            print("未找到有效的发送地址")
            return
        
        # 3. 验证地址有效性
        valid_addresses, invalid_addresses = self.validate_addresses(sender_addresses)
        if not valid_addresses:
            print("没有有效的地址可以查询")
            return
        
        # 4. 查询交易记录，根据时间和金额筛选小曾的真实收款地址
        receive_addresses, transaction_records, address_stats = self.query_sender_transactions_with_time_filter(valid_addresses, address_info)
        
        # 5. 保存结果
        if receive_addresses or transaction_records:
            self.save_results(receive_addresses, transaction_records, address_stats)
        
        print(f"\n{'='*60}")
        print("分析完成！")
        print(f"{'='*60}")
        print(f"发送地址总数: {len(sender_addresses)}")
        print(f"有效发送地址: {len(valid_addresses)}")
        print(f"小曾真实收款地址: {len(receive_addresses)}")
        print(f"符合条件的交易记录: {len(transaction_records)}")
        print(f"{'='*60}")
        
        if receive_addresses:
            print("🎯 小曾真实收款地址:")
            for addr in receive_addresses:
                stats = address_stats[addr]
                print(f"  {addr} (被{len(stats['sender_addresses'])}个地址转账，总额{stats['total_amount']:.2f} USDT)")
        else:
            print("⚠️  未找到符合条件的真实收款地址")
        print(f"{'='*60}")

def query_specific_transaction(address, amount, tx_time_str, time_range_hours=16):
    """查询指定交易"""
    print("="*60)
    print(f"查询指定交易 - {address}")
    print("="*60)
    
    print(f"查询交易信息:")
    print(f"  地址: {address}")
    print(f"  金额: {amount} USDT")
    print(f"  时间: {tx_time_str}")
    
    analyzer = XiaozengAnalyzer()
    
    try:
        # 解析时间
        if '/' in tx_time_str:
            # 处理 2023/9/22 10:50 格式
            tx_time = datetime.strptime(tx_time_str.replace('/', '-'), '%Y-%m-%d %H:%M')
        else:
            tx_time = datetime.strptime(tx_time_str, '%Y-%m-%d %H:%M:%S')
        print(f"  解析后时间: {tx_time}")
        
        # 设置查询时间范围
        start_time = tx_time - timedelta(hours=time_range_hours)
        end_time = tx_time + timedelta(hours=time_range_hours)
        
        print(f"\n查询时间范围 (±{time_range_hours}小时):")
        print(f"  开始时间: {start_time}")
        print(f"  结束时间: {end_time}")
        
        # 先查询该地址的所有USDT交易（不限时间）
        print(f"\n正在查询地址 {address} 的所有USDT交易...")
        all_transactions = analyzer.collector.query_trc20_usdt_transactions(address, limit=100)
        print(f"找到 {len(all_transactions)} 条总USDT交易")
        
        # 再查询指定时间范围内的交易
        print(f"\n正在查询指定时间范围内的USDT交易...")
        transactions = analyzer.collector.query_trc20_usdt_transactions_with_time_filter(
            address, start_time, end_time, limit=50)
        
        print(f"找到 {len(transactions)} 条时间范围内的USDT交易")
        
        # 如果时间范围内没有交易，分析所有交易
        if not transactions and all_transactions:
            print("时间范围内无交易，分析所有交易中接近目标金额的记录...")
            transactions = all_transactions
        elif not transactions and not all_transactions:
            print("未找到任何交易记录")
            return
        
        # 分析交易记录
        print(f"\n交易记录分析:")
        print("-" * 80)
        
        matched_transactions = []
        
        for i, tx in enumerate(transactions, 1):
            from_addr = tx.get('from_address', '')
            to_addr = tx.get('to_address', '')
            tx_amount = tx.get('amount_usdt', 0)
            tx_timestamp = tx.get('timestamp', '')
            tx_hash = tx.get('tx_hash', '')
            
            print(f"\n交易 {i}:")
            print(f"  交易哈希: {tx_hash}")
            print(f"  发送地址: {from_addr}")
            print(f"  接收地址: {to_addr}")
            print(f"  金额: {tx_amount} USDT")
            print(f"  时间: {tx_timestamp}")
            
            # 检查是否匹配目标交易
            is_sender = from_addr.lower() == address.lower()
            is_receiver = to_addr.lower() == address.lower()
            amount_match = abs(tx_amount - amount) < 10  # 金额误差在10 USDT内
            
            print(f"  地址匹配: 发送方={is_sender}, 接收方={is_receiver}")
            print(f"  金额匹配: {amount_match} (差值: {abs(tx_amount - amount):.2f})")
            
            # 时间匹配检查
            time_match = False
            if isinstance(tx_timestamp, datetime):
                time_diff = abs((tx_timestamp - tx_time).total_seconds())
                time_match = time_diff <= (time_range_hours * 3600)  # 时间范围内
                print(f"  时间匹配: {time_match} (差值: {time_diff/3600:.1f}小时)")
            
            # 如果是相关交易，标记为匹配
            if (is_sender or is_receiver) and amount_match and time_match:
                matched_transactions.append(tx)
                print(f"  ✅ 匹配的交易!")
                
                # 如果是发送方，显示接收地址（可能是小曾的收款地址）
                if is_sender:
                    print(f"  🎯 可能的小曾收款地址: {to_addr}")
            else:
                print(f"  ❌ 不匹配")
        
        print(f"\n{'='*60}")
        print(f"查询结果总结:")
        print(f"  总交易数: {len(transactions)}")
        print(f"  匹配交易数: {len(matched_transactions)}")
        
        if matched_transactions:
            print(f"\n🎯 匹配的交易详情:")
            for i, tx in enumerate(matched_transactions, 1):
                print(f"  交易 {i}:")
                print(f"    哈希: {tx.get('tx_hash', '')}")
                print(f"    发送: {tx.get('from_address', '')}")
                print(f"    接收: {tx.get('to_address', '')}")
                print(f"    金额: {tx.get('amount_usdt', 0)} USDT")
                print(f"    时间: {tx.get('timestamp', '')}")
        else:
            print("  ⚠️ 未找到完全匹配的交易")
        
        print(f"{'='*60}")
        
        return matched_transactions
        
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_specific_addresses():
    """测试指定的两个地址"""
    print("="*60)
    print("测试指定地址")
    print("="*60)
    
    # 测试地址
    test_addresses = [
        {
            'address': 'TSPWd3iExhAMwfB5c7eWb49dMFHMpKegB6',
            'amount': 3773.0,
            'date': '2023/10/3 2:28'
        },
        {
            'address': 'TLWWmTN7rRU4SbEdMRpMq6xhM4afRM9gSj', 
            'amount': 1362.0,
            'date': '2023/9/22 10:50'
        }
    ]
    
    analyzer = XiaozengAnalyzer()
    
    print(f"开始测试 {len(test_addresses)} 个地址...")
    
    for i, addr_info in enumerate(test_addresses, 1):
        address = addr_info['address']
        amount = addr_info['amount']
        date = addr_info['date']
        
        print(f"\n--- 测试地址 {i} ---")
        print(f"地址: {address}")
        print(f"金额: {amount} USDT")
        print(f"时间: {date}")
        
        # 验证地址格式
        try:
            result = analyzer.validator.validate_address(address)
            print(f"地址验证结果: {result}")
            
            if result['format_valid']:
                print("✓ 地址格式有效")
            else:
                print("✗ 地址格式无效")
                continue
                
        except Exception as e:
            print(f"地址验证失败: {e}")
            continue
        
        print(f"地址 {i} 测试完成")
    
    print(f"\n{'='*60}")
    print("指定地址测试完成")
    print(f"{'='*60}")

def batch_test_all_addresses():
    """批量测试小曾Excel文档中的所有有效地址"""
    print("="*80)
    print("批量测试小曾Excel文档中的所有有效地址")
    print("="*80)
    
    analyzer = XiaozengAnalyzer()
    
    # 1. 读取充值记录
    print("正在读取小曾币安充值记录...")
    deposits = analyzer.read_xiaozeng_deposits()
    if deposits is None:
        print("无法读取充值记录，退出测试")
        return
    
    # 2. 提取发送地址和相关信息
    sender_addresses, address_info = analyzer.extract_sender_addresses(deposits)
    if not sender_addresses:
        print("未找到有效的发送地址")
        return
    
    print(f"找到 {len(sender_addresses)} 个发送地址，开始批量测试...")
    
    # 3. 批量测试每个地址
    all_matched_transactions = []
    receive_address_stats = {}
    
    for i, addr_info in enumerate(address_info, 1):
        address = addr_info['address']
        amount = addr_info['amount']
        deposit_time = addr_info['time']
        
        print(f"\n{'='*60}")
        print(f"测试地址 {i}/{len(address_info)}: {address}")
        print(f"金额: {amount} USDT, 时间: {deposit_time}")
        print(f"{'='*60}")
        
        # 格式化时间字符串
        if isinstance(deposit_time, datetime):
            time_str = deposit_time.strftime('%Y-%m-%d %H:%M:%S')
        else:
            time_str = str(deposit_time)
        
        try:
            # 查询该地址的匹配交易
            matched_txs = query_specific_transaction(address, amount, time_str, time_range_hours=16)
            
            if matched_txs:
                print(f"✅ 地址 {address} 找到 {len(matched_txs)} 个匹配交易")
                all_matched_transactions.extend(matched_txs)
                
                # 统计接收地址
                for tx in matched_txs:
                    receive_addr = tx.get('to_address', '')
                    if receive_addr:
                        if receive_addr not in receive_address_stats:
                            receive_address_stats[receive_addr] = {
                                'count': 0,
                                'sender_addresses': set(),
                                'total_amount': 0,
                                'transactions': []
                            }
                        
                        receive_address_stats[receive_addr]['count'] += 1
                        receive_address_stats[receive_addr]['sender_addresses'].add(address)
                        receive_address_stats[receive_addr]['total_amount'] += tx.get('amount_usdt', 0)
                        receive_address_stats[receive_addr]['transactions'].append(tx)
                        
                        print(f"  🎯 收款地址: {receive_addr} (金额: {tx.get('amount_usdt', 0)} USDT)")
            else:
                print(f"❌ 地址 {address} 未找到匹配交易")
                
        except Exception as e:
            print(f"❌ 地址 {address} 测试失败: {e}")
        
        # 避免API频率限制
        time.sleep(1)
    
    # 4. 分析结果
    print(f"\n{'='*80}")
    print("批量测试结果分析")
    print(f"{'='*80}")
    
    print(f"总测试地址数: {len(address_info)}")
    print(f"找到匹配交易的地址数: {len([tx for tx in all_matched_transactions])}")
    print(f"总匹配交易数: {len(all_matched_transactions)}")
    print(f"发现的收款地址数: {len(receive_address_stats)}")
    
    # 5. 分析收款地址统计
    if receive_address_stats:
        print(f"\n🎯 收款地址统计分析:")
        sorted_addresses = sorted(receive_address_stats.items(), 
                                key=lambda x: len(x[1]['sender_addresses']), 
                                reverse=True)
        
        xiaozeng_real_addresses = []
        
        for receive_addr, stats in sorted_addresses:
            sender_count = len(stats['sender_addresses'])
            total_amount = stats['total_amount']
            transaction_count = stats['count']
            
            print(f"\n地址: {receive_addr}")
            print(f"  被 {sender_count} 个不同发送地址转账")
            print(f"  总交易次数: {transaction_count}")
            print(f"  总金额: {total_amount:.2f} USDT")
            print(f"  发送地址: {', '.join(list(stats['sender_addresses'])[:3])}{'...' if sender_count > 3 else ''}")
            
            # 如果被2个或以上不同发送地址转账，认为是小曾的真实收款地址
            if sender_count >= 2:
                xiaozeng_real_addresses.append(receive_addr)
                print(f"  ✅ 认定为小曾真实收款地址")
            else:
                print(f"  ❌ 发送地址数量不足，不认定为真实收款地址")
        
        # 6. 保存结果
        save_batch_test_results(all_matched_transactions, receive_address_stats, xiaozeng_real_addresses)
        
        print(f"\n🎉 最终结果:")
        print(f"  小曾真实收款地址: {len(xiaozeng_real_addresses)} 个")
        if xiaozeng_real_addresses:
            for addr in xiaozeng_real_addresses:
                stats = receive_address_stats[addr]
                print(f"    {addr} (被{len(stats['sender_addresses'])}个地址转账，总额{stats['total_amount']:.2f} USDT)")
    
    print(f"{'='*80}")

def save_batch_test_results(matched_transactions, receive_address_stats, xiaozeng_real_addresses):
    """保存批量测试结果"""
    print(f"\n保存批量测试结果...")
    
    # 保存到测试结果文件
    with open('xiaozeng_test_results.txt', 'a', encoding='utf-8') as f:
        f.write(f"\n\n## 批量测试结果 - {datetime.now()}\n")
        f.write(f"总匹配交易数: {len(matched_transactions)}\n")
        f.write(f"发现的收款地址数: {len(receive_address_stats)}\n")
        f.write(f"小曾真实收款地址数: {len(xiaozeng_real_addresses)}\n\n")
        
        if xiaozeng_real_addresses:
            f.write("### 小曾真实收款地址:\n")
            for addr in xiaozeng_real_addresses:
                stats = receive_address_stats[addr]
                f.write(f"- {addr}\n")
                f.write(f"  被 {len(stats['sender_addresses'])} 个发送地址转账\n")
                f.write(f"  总金额: {stats['total_amount']:.2f} USDT\n")
                f.write(f"  发送地址: {', '.join(stats['sender_addresses'])}\n\n")
        
        f.write("### 所有匹配交易记录:\n")
        for i, tx in enumerate(matched_transactions, 1):
            f.write(f"{i}. 交易哈希: {tx.get('tx_hash', '')}\n")
            f.write(f"   发送: {tx.get('from_address', '')} → 接收: {tx.get('to_address', '')}\n")
            f.write(f"   金额: {tx.get('amount_usdt', 0)} USDT\n")
            f.write(f"   时间: {tx.get('timestamp', '')}\n\n")
    
    print(f"  结果已保存到: xiaozeng_test_results.txt")

def main():
    """主函数"""
    # 批量测试所有地址
    batch_test_all_addresses()
    
    # 如果需要测试单个地址，取消下面的注释
    # query_specific_transaction('TSPWd3iExhAMwfB5c7eWb49dMFHMpKegB6', 3773.0, '2023/10/3 2:28')

if __name__ == "__main__":
    main()