#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量查询小曾交易记录 - 实现readme任务2
通过链上查询获取小曾所有币安充值收款地址
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.data_reader import ExcelDataReader
from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
import pandas as pd
from datetime import datetime
import time

class XiaozengAnalyzer:
    """小曾交易记录分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.reader = ExcelDataReader(".")
        self.validator = AddressValidator()
        self.collector = AddressCollector(self.reader)
        
    def read_xiaozeng_deposits(self):
        """读取小曾币安充值记录"""
        try:
            print("正在读取小曾币安充值记录...")
            deposits = self.reader.read_xiaozeng_binance_deposits()
            print(f"成功读取 {len(deposits)} 条充值记录")
            
            # 显示数据结构
            print("\n数据列名:")
            for i, col in enumerate(deposits.columns):
                print(f"  {i}: {col}")
            
            # 显示前几条记录
            print(f"\n前5条记录:")
            print(deposits.head())
            
            return deposits
            
        except Exception as e:
            print(f"读取充值记录失败: {e}")
            return None
    
    def extract_sender_addresses(self, deposits):
        """从充值记录中提取发送地址"""
        print("\n开始提取发送地址...")
        
        sender_addresses = []
        address_info = []
        
        for index, record in deposits.iterrows():
            sender_addr = str(record['发送地址']).strip()
            usdt_amount = record['USDT对应数额']
            deposit_time = record['时间']
            
            # 过滤掉纯数字ID（这些不是区块链地址）
            if sender_addr.isdigit():
                print(f"跳过数字ID: {sender_addr}")
                continue
            
            # 验证地址格式
            if len(sender_addr) >= 30:  # 区块链地址通常比较长
                sender_addresses.append(sender_addr)
                address_info.append({
                    'address': sender_addr,
                    'amount': usdt_amount,
                    'time': deposit_time,
                    'record_index': index
                })
                print(f"提取地址: {sender_addr} (金额: {usdt_amount} USDT, 时间: {deposit_time})")
        
        print(f"\n总共提取到 {len(sender_addresses)} 个有效地址")
        return sender_addresses, address_info
    
    def validate_addresses(self, addresses):
        """批量验证地址有效性"""
        print(f"\n开始验证 {len(addresses)} 个地址...")
        
        valid_addresses = []
        invalid_addresses = []
        
        for i, addr in enumerate(addresses, 1):
            print(f"验证地址 {i}/{len(addresses)}: {addr}")
            
            try:
                result = self.validator.validate_address(addr)
                
                if result['format_valid'] and result['exists_on_chain']:
                    valid_addresses.append(addr)
                    print(f"  ✓ 有效 ({result['address_type']})")
                else:
                    invalid_addresses.append(addr)
                    print(f"  ✗ 无效 ({', '.join(result['errors'])})")
                    
                # 避免API频率限制
                time.sleep(0.5)
                
            except Exception as e:
                invalid_addresses.append(addr)
                print(f"  ✗ 验证失败: {e}")
        
        print(f"\n地址验证完成:")
        print(f"  有效地址: {len(valid_addresses)}")
        print(f"  无效地址: {len(invalid_addresses)}")
        
        return valid_addresses, invalid_addresses
    
    def query_sender_transactions_with_time_filter(self, addresses, address_info):
        """查询发送地址的USDT交易，根据时间和金额筛选小曾的真实收款地址"""
        print(f"\n开始查询 {len(addresses)} 个地址的USDT交易...")
        print("筛选条件: 时间匹配±1小时，金额误差<3 USDT")
        
        # 用于统计每个收款地址被多少个发送地址转账过
        receive_address_count = {}
        transaction_records = []
        
        for i, addr in enumerate(addresses, 1):
            print(f"\n查询地址 {i}/{len(addresses)}: {addr}")
            
            try:
                # 获取该地址对应的充值信息
                addr_info = next((info for info in address_info if info['address'] == addr), None)
                if not addr_info:
                    print(f"  跳过：未找到对应的充值记录")
                    continue
                
                deposit_time = addr_info['time']
                deposit_amount = addr_info['amount']
                
                print(f"  充值时间: {deposit_time}")
                print(f"  充值金额: {deposit_amount} USDT")
                
                # 查询USDT交易
                transactions = self.collector.query_trc20_usdt_transactions(addr, limit=100)
                print(f"  找到 {len(transactions)} 条USDT交易")
                
                # 筛选符合条件的交易
                matched_transactions = []
                for tx in transactions:
                    from_addr = tx.get('from_address', '')
                    to_addr = tx.get('to_address', '')
                    amount = tx.get('amount_usdt', 0)
                    tx_time = tx.get('timestamp', '')
                    
                    # 只处理该地址作为发送方的交易
                    if from_addr.lower() != addr.lower():
                        continue
                    
                    # 时间筛选：±1小时
                    time_diff = None
                    if isinstance(tx_time, datetime) and isinstance(deposit_time, datetime):
                        time_diff = abs((tx_time - deposit_time).total_seconds())
                        if time_diff > 3600:  # 1小时 = 3600秒
                            continue
                    else:
                        continue
                    
                    # 金额匹配：误差<3 USDT
                    amount_diff = abs(amount - deposit_amount)
                    if amount_diff >= 3:
                        continue
                    
                    matched_transactions.append({
                        'sender_address': from_addr,
                        'receive_address': to_addr,
                        'amount_usdt': amount,
                        'tx_hash': tx.get('tx_hash', ''),
                        'tx_timestamp': tx_time,
                        'deposit_amount': deposit_amount,
                        'deposit_time': deposit_time,
                        'time_diff_seconds': time_diff if isinstance(tx_time, datetime) and isinstance(deposit_time, datetime) else None
                    })
                    
                    # 统计收款地址出现次数
                    if to_addr not in receive_address_count:
                        receive_address_count[to_addr] = {
                            'count': 0,
                            'sender_addresses': set(),
                            'total_amount': 0,
                            'transactions': []
                        }
                    
                    receive_address_count[to_addr]['count'] += 1
                    receive_address_count[to_addr]['sender_addresses'].add(from_addr)
                    receive_address_count[to_addr]['total_amount'] += amount
                    receive_address_count[to_addr]['transactions'].append(matched_transactions[-1])
                    
                    print(f"    ✓ 匹配: {to_addr} ({amount} USDT, 时间差{time_diff/3600:.1f}h, 金额差{amount_diff:.1f})")
                
                transaction_records.extend(matched_transactions)
                print(f"  符合条件的交易: {len(matched_transactions)} 条")
                
                # 避免API频率限制
                time.sleep(1)
                
            except Exception as e:
                print(f"  查询失败: {e}")
        
        # 分析交集：找出被多个发送地址转账的收款地址
        print(f"\n📊 收款地址统计分析:")
        print(f"总共发现 {len(receive_address_count)} 个收款地址")
        
        # 按发送地址数量排序
        sorted_addresses = sorted(receive_address_count.items(), 
                                key=lambda x: len(x[1]['sender_addresses']), 
                                reverse=True)
        
        xiaozeng_real_addresses = []
        
        print(f"\n收款地址详细统计:")
        for receive_addr, stats in sorted_addresses:
            sender_count = len(stats['sender_addresses'])
            total_amount = stats['total_amount']
            transaction_count = stats['count']
            
            print(f"\n地址: {receive_addr}")
            print(f"  被 {sender_count} 个不同发送地址转账")
            print(f"  总交易次数: {transaction_count}")
            print(f"  总金额: {total_amount:.2f} USDT")
            print(f"  发送地址: {', '.join(list(stats['sender_addresses'])[:3])}{'...' if sender_count > 3 else ''}")
            
            # 交集逻辑：被3个或以上不同发送地址转账的才认为是小曾的真实收款地址
            if sender_count >= 3:
                xiaozeng_real_addresses.append(receive_addr)
                print(f"  ✅ 认定为小曾真实收款地址")
            else:
                print(f"  ❌ 发送地址数量不足，不认定为真实收款地址")
        
        print(f"\n🎯 最终结果:")
        print(f"  小曾真实收款地址: {len(xiaozeng_real_addresses)} 个")
        print(f"  符合条件的交易记录: {len(transaction_records)} 条")
        
        return xiaozeng_real_addresses, transaction_records, receive_address_count
    
    def save_results(self, receive_addresses, transaction_records, address_stats):
        """保存分析结果"""
        print(f"\n保存分析结果...")
        
        # 保存小曾真实收款地址
        with open('xiaozeng_real_receive_addresses.txt', 'w', encoding='utf-8') as f:
            f.write("# 小曾真实币安充值收款地址（交集分析结果）\n")
            f.write(f"# 生成时间: {datetime.now()}\n")
            f.write(f"# 筛选条件: 时间匹配±1小时，金额误差<3 USDT，被≥3个发送地址转账\n")
            f.write(f"# 总计: {len(receive_addresses)} 个地址\n\n")
            
            for addr in receive_addresses:
                stats = address_stats[addr]
                f.write(f"{addr}\n")
                f.write(f"  # 被 {len(stats['sender_addresses'])} 个发送地址转账，总金额: {stats['total_amount']:.2f} USDT\n\n")
        
        print(f"  真实收款地址已保存到: xiaozeng_real_receive_addresses.txt")
        
        # 保存详细统计信息
        with open('xiaozeng_address_statistics.txt', 'w', encoding='utf-8') as f:
            f.write("# 小曾收款地址统计分析\n")
            f.write(f"# 生成时间: {datetime.now()}\n\n")
            
            sorted_addresses = sorted(address_stats.items(), 
                                    key=lambda x: len(x[1]['sender_addresses']), 
                                    reverse=True)
            
            for receive_addr, stats in sorted_addresses:
                sender_count = len(stats['sender_addresses'])
                f.write(f"地址: {receive_addr}\n")
                f.write(f"  发送地址数量: {sender_count}\n")
                f.write(f"  交易次数: {stats['count']}\n")
                f.write(f"  总金额: {stats['total_amount']:.2f} USDT\n")
                f.write(f"  是否为真实地址: {'是' if sender_count >= 3 else '否'}\n")
                f.write(f"  发送地址列表: {', '.join(stats['sender_addresses'])}\n\n")
        
        print(f"  统计信息已保存到: xiaozeng_address_statistics.txt")
        
        # 保存交易记录
        if transaction_records:
            df = pd.DataFrame(transaction_records)
            df.to_csv('xiaozeng_filtered_transactions.csv', index=False, encoding='utf-8-sig')
            print(f"  筛选后的交易记录已保存到: xiaozeng_filtered_transactions.csv")
    
    def analyze_xiaozeng_deposits(self):
        """分析小曾的充值记录，获取发送地址"""
        print("="*60)
        print("小曾收款地址获取分析 - 任务2")
        print("="*60)
        
        # 1. 读取充值记录
        deposits = self.read_xiaozeng_deposits()
        if deposits is None:
            return
        
        # 2. 提取发送地址
        sender_addresses, address_info = self.extract_sender_addresses(deposits)
        if not sender_addresses:
            print("未找到有效的发送地址")
            return
        
        # 3. 验证地址有效性
        valid_addresses, invalid_addresses = self.validate_addresses(sender_addresses)
        if not valid_addresses:
            print("没有有效的地址可以查询")
            return
        
        # 4. 查询交易记录，根据时间和金额筛选小曾的真实收款地址
        receive_addresses, transaction_records, address_stats = self.query_sender_transactions_with_time_filter(valid_addresses, address_info)
        
        # 5. 保存结果
        if receive_addresses or transaction_records:
            self.save_results(receive_addresses, transaction_records, address_stats)
        
        print(f"\n{'='*60}")
        print("分析完成！")
        print(f"{'='*60}")
        print(f"发送地址总数: {len(sender_addresses)}")
        print(f"有效发送地址: {len(valid_addresses)}")
        print(f"小曾真实收款地址: {len(receive_addresses)}")
        print(f"符合条件的交易记录: {len(transaction_records)}")
        print(f"{'='*60}")
        
        if receive_addresses:
            print("🎯 小曾真实收款地址:")
            for addr in receive_addresses:
                stats = address_stats[addr]
                print(f"  {addr} (被{len(stats['sender_addresses'])}个地址转账，总额{stats['total_amount']:.2f} USDT)")
        else:
            print("⚠️  未找到符合条件的真实收款地址")
        print(f"{'='*60}")

def main():
    """主函数"""
    analyzer = XiaozengAnalyzer()
    analyzer.analyze_xiaozeng_deposits()

if __name__ == "__main__":
    main()