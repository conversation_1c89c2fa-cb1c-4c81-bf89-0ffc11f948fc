#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MoneyTrace 主程序
实现0.简易分析模块和2.小曾收款地址获取模块
"""

import sys
import os
from pathlib import Path

# 添加模块路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "modules"))

from modules.data_reader import ExcelDataReader
from modules.simple_analyzer import SimpleAnalyzer
from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("=" * 80)
    print("MoneyTrace 资金流向追踪分析系统")
    print("=" * 80)
    
    # 设置工作目录为项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print(f"工作目录: {os.getcwd()}")
    
    try:
        # 创建数据读取器
        print("\n1. 初始化数据读取器...")
        reader = ExcelDataReader(".")
        
        # 分析所有文件结构
        print("\n2. 分析Excel文件结构...")
        file_analysis = reader.analyze_all_files()
        
        # 显示文件分析结果
        print("\n文件分析结果:")
        print("-" * 60)
        for filename, info in file_analysis.items():
            if info.get('exists') and 'error' not in info:
                print(f"✓ {filename}: {info['rows']}行 x {info['columns']}列 - {info['description']}")
            else:
                print(f"✗ {filename}: {info.get('error', '未知错误')}")
        
        # 运行简易分析
        print("\n3. 执行0.简易分析模块...")
        print("-" * 60)
        analyzer = SimpleAnalyzer(reader)
        result = analyzer.run_simple_analysis()
        
        # 显示分析结果
        print("\n" + "=" * 80)
        print("0.简易分析模块 - 分析结果报告")
        print("=" * 80)
        
        print(f"\n📅 分析时间段:")
        print(f"   开始时间: {result['analysis_period']['start']}")
        print(f"   结束时间: {result['analysis_period']['end']}")
        print(f"   分析天数: {result['analysis_period']['duration_days']} 天")
        
        print(f"\n💰 小江币安充值情况:")
        print(f"   充值记录数: {result['xiajiang_deposits']['record_count']} 条")
        print(f"   充值USDT总额: {result['xiajiang_deposits']['total_usdt']:,.2f} USDT")
        
        print(f"\n🔄 小江P2P卖出情况:")
        print(f"   P2P卖出记录数: {result['xiajiang_p2p_sales']['record_count']} 条")
        print(f"   P2P卖出USDT总额: {result['xiajiang_p2p_sales']['total_usdt']:,.2f} USDT")
        
        print(f"\n🏦 法币收款匹配情况:")
        print(f"   匹配证据链数: {result['matched_fiat_receipts']['evidence_count']} 条")
        print(f"   可排除USDT金额: {result['matched_fiat_receipts']['matched_usdt']:,.2f} USDT")
        print(f"   法币收款总金额: {result['matched_fiat_receipts']['total_fiat_cny']:,.2f} CNY")
        print(f"   平均汇率: {result['matched_fiat_receipts']['average_exchange_rate']:.2f} CNY/USDT")
        
        print(f"\n📊 最终计算结果:")
        print(f"   小江充值USDT总额: {result['final_calculation']['total_deposits_usdt']:,.2f} USDT")
        print(f"   小江P2P卖出USDT总额: {result['final_calculation']['total_p2p_sales_usdt']:,.2f} USDT")
        print(f"   可排除USDT金额: {result['final_calculation']['excluded_usdt_with_fiat_proof']:,.2f} USDT")
        print(f"   法币收款总金额: {result['final_calculation']['total_fiat_received_cny']:,.2f} CNY")
        print(f"   嫌疑流向小曾的USDT: {result['final_calculation']['suspected_flow_to_xiaozeng_usdt']:,.2f} USDT")
        print(f"   排除率: {result['final_calculation']['exclusion_rate']:.2f}%")
        print(f"   P2P覆盖率: {result['final_calculation']['p2p_coverage_rate']:.2f}%")
        
        # 保存分析结果
        print(f"\n4. 保存分析结果...")
        save_analysis_result(result)
        
        print(f"\n✅ 0.简易分析模块执行完成！")
        
        # 运行2.小曾收款地址获取模块（改进版）
        print(f"\n5. 执行2.小曾收款地址获取模块（改进版）...")
        print("-" * 60)

        # 创建地址收集器
        address_collector = AddressCollector(reader)
        collection_result = address_collector.collect_xiaozeng_addresses()

        print(f"\n📍 小曾收款地址分析结果:")
        print(f"   发送地址总数: {collection_result['summary']['total_sender_addresses']} 个")
        print(f"   有效发送地址: {collection_result['summary']['valid_sender_addresses']} 个")
        print(f"   小曾收款地址: {collection_result['summary']['xiaozeng_receive_addresses_found']} 个")
        print(f"   交易记录: {collection_result['summary']['transaction_records_found']} 条")

        # 显示收款地址
        receive_addresses = collection_result['xiaozeng_receive_addresses']['addresses']
        if receive_addresses:
            print(f"\n   小曾收款地址列表:")
            for i, addr in enumerate(receive_addresses[:10], 1):  # 只显示前10个
                print(f"   {i}. {addr}")
            if len(receive_addresses) > 10:
                print(f"   ... 还有 {len(receive_addresses) - 10} 个地址")

        # 保存详细结果
        save_address_collection_results(collection_result)

        print(f"\n✅ 2.小曾收款地址获取模块执行完成！")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        return 1
    
    return 0

def save_analysis_result(result):
    """保存分析结果到文件"""
    output_dir = Path("data/output")
    output_dir.mkdir(exist_ok=True)
    
    # 保存详细结果到文本文件
    with open(output_dir / "simple_analysis_report.txt", "w", encoding="utf-8") as f:
        f.write("MoneyTrace 简易分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"分析时间段: {result['analysis_period']['start']} 到 {result['analysis_period']['end']}\n")
        f.write(f"分析天数: {result['analysis_period']['duration_days']} 天\n\n")
        
        f.write("小江币安充值情况:\n")
        f.write(f"  充值记录数: {result['xiajiang_deposits']['record_count']} 条\n")
        f.write(f"  充值USDT总额: {result['xiajiang_deposits']['total_usdt']:,.2f} USDT\n\n")
        
        f.write("小江P2P卖出情况:\n")
        f.write(f"  P2P卖出记录数: {result['xiajiang_p2p_sales']['record_count']} 条\n")
        f.write(f"  P2P卖出USDT总额: {result['xiajiang_p2p_sales']['total_usdt']:,.2f} USDT\n\n")

        f.write("法币收款匹配情况:\n")
        f.write(f"  匹配证据链数: {result['matched_fiat_receipts']['evidence_count']} 条\n")
        f.write(f"  可排除USDT金额: {result['matched_fiat_receipts']['matched_usdt']:,.2f} USDT\n")
        f.write(f"  法币收款总金额: {result['matched_fiat_receipts']['total_fiat_cny']:,.2f} CNY\n")
        f.write(f"  平均汇率: {result['matched_fiat_receipts']['average_exchange_rate']:.2f} CNY/USDT\n\n")
        
        f.write("最终计算结果:\n")
        f.write(f"  小江充值USDT总额: {result['final_calculation']['total_deposits_usdt']:,.2f} USDT\n")
        f.write(f"  小江P2P卖出USDT总额: {result['final_calculation']['total_p2p_sales_usdt']:,.2f} USDT\n")
        f.write(f"  可排除USDT金额: {result['final_calculation']['excluded_usdt_with_fiat_proof']:,.2f} USDT\n")
        f.write(f"  法币收款总金额: {result['final_calculation']['total_fiat_received_cny']:,.2f} CNY\n")
        f.write(f"  嫌疑流向小曾的USDT: {result['final_calculation']['suspected_flow_to_xiaozeng_usdt']:,.2f} USDT\n")
        f.write(f"  排除率: {result['final_calculation']['exclusion_rate']:.2f}%\n")
        f.write(f"  P2P覆盖率: {result['final_calculation']['p2p_coverage_rate']:.2f}%\n\n")
        
        # 保存证据链详情
        f.write("证据链详情:\n")
        f.write("-" * 30 + "\n")
        for i, evidence in enumerate(result['matched_fiat_receipts']['evidence_chain'], 1):
            f.write(f"证据链 {i}:\n")
            f.write(f"  P2P卖出USDT: {evidence['usdt_amount']:.2f}\n")
            f.write(f"  法币金额: {evidence['cny_amount']:.2f} CNY\n")
            f.write(f"  支付方式: {evidence['payment_method']}\n")
            f.write(f"  P2P卖出时间: {evidence['p2p_time']}\n")
            f.write(f"  法币收款时间: {evidence['receipt_time']}\n")
            f.write(f"  时间差: {evidence['time_diff_hours']:.1f} 小时\n")
            f.write("\n")
    
    print(f"   分析报告已保存到: {output_dir / 'simple_analysis_report.txt'}")

def extract_xiaozeng_addresses(reader):
    """提取小曾的收款地址"""
    # 读取小曾币安充值记录
    xz_deposits = reader.read_xiaozeng_binance_deposits()

    # 提取发送地址
    addresses = set()
    for _, record in xz_deposits.iterrows():
        sender_addr = record['发送地址']
        if pd.notna(sender_addr):
            # 处理字符串和数字类型
            if isinstance(sender_addr, str):
                addr_str = sender_addr.strip()
                if addr_str:
                    addresses.add(addr_str)
            elif isinstance(sender_addr, (int, float)):
                # 如果是数字，转换为字符串
                addr_str = str(sender_addr).strip()
                if addr_str and addr_str != 'nan':
                    addresses.add(addr_str)

    return sorted(list(addresses))

def save_address_collection_results(collection_result):
    """保存地址收集结果到文件"""
    output_dir = Path("data/output")
    output_dir.mkdir(exist_ok=True)

    # 保存小曾收款地址
    receive_addresses = collection_result['xiaozeng_receive_addresses']['addresses']
    with open(output_dir / "xiaozeng_verified_addresses.txt", "w", encoding="utf-8") as f:
        f.write("小曾收款地址列表（验证版）\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"总计发现 {len(receive_addresses)} 个收款地址:\n\n")

        trc20_addrs = collection_result['xiaozeng_receive_addresses']['trc20_addresses']
        erc20_addrs = collection_result['xiaozeng_receive_addresses']['erc20_addresses']

        f.write(f"TRC20地址 ({len(trc20_addrs)} 个):\n")
        for i, addr in enumerate(trc20_addrs, 1):
            f.write(f"  {i}. {addr}\n")

        f.write(f"\nERC20地址 ({len(erc20_addrs)} 个):\n")
        for i, addr in enumerate(erc20_addrs, 1):
            f.write(f"  {i}. {addr}\n")

    # 保存无效地址
    invalid_addresses = collection_result['sender_addresses']['invalid']
    with open(output_dir / "invalid_addresses.txt", "w", encoding="utf-8") as f:
        f.write("无效地址列表（PDF扫描错误）\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"总计 {len(invalid_addresses)} 个无效地址:\n\n")

        for i, addr in enumerate(invalid_addresses, 1):
            f.write(f"{i}. {addr}\n")

    # 保存地址验证报告
    with open(output_dir / "address_validation_report.txt", "w", encoding="utf-8") as f:
        f.write("地址验证报告\n")
        f.write("=" * 30 + "\n\n")

        f.write("发送地址验证结果:\n")
        f.write(f"  总地址数: {collection_result['sender_addresses']['total']}\n")
        f.write(f"  有效地址: {len(collection_result['sender_addresses']['valid'])}\n")
        f.write(f"  无效地址: {len(collection_result['sender_addresses']['invalid'])}\n")
        f.write(f"  忽略地址: {len(collection_result['sender_addresses']['ignored'])}\n\n")

        f.write("小曾收款地址结果:\n")
        f.write(f"  总收款地址: {collection_result['xiaozeng_receive_addresses']['total']}\n")
        f.write(f"  TRC20地址: {len(trc20_addrs)}\n")
        f.write(f"  ERC20地址: {len(erc20_addrs)}\n")
        f.write(f"  交易记录: {len(collection_result['transaction_records'])}\n\n")

        # 详细验证信息
        f.write("详细验证信息:\n")
        f.write("-" * 20 + "\n")
        for detail in collection_result['sender_addresses']['validation_details']:
            f.write(f"地址: {detail['address']}\n")
            f.write(f"  类型: {detail['address_type']}\n")
            f.write(f"  格式有效: {detail['format_valid']}\n")
            f.write(f"  链上存在: {detail['exists_on_chain']}\n")
            if detail['errors']:
                f.write(f"  错误: {'; '.join(detail['errors'])}\n")
            f.write("\n")

    print(f"   小曾收款地址已保存到: {output_dir / 'xiaozeng_verified_addresses.txt'}")
    print(f"   无效地址已保存到: {output_dir / 'invalid_addresses.txt'}")
    print(f"   验证报告已保存到: {output_dir / 'address_validation_report.txt'}")

def save_xiaozeng_addresses(addresses):
    """保存小曾收款地址到文件（旧版本，保持兼容性）"""
    output_dir = Path("data/output")
    output_dir.mkdir(exist_ok=True)

    with open(output_dir / "xiaozeng_receive_addresses_old.txt", "w", encoding="utf-8") as f:
        f.write("小曾收款地址列表（旧版本）\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"总计发现 {len(addresses)} 个收款地址:\n\n")

        for i, addr in enumerate(addresses, 1):
            f.write(f"{i}. {addr}\n")

    print(f"   小曾收款地址（旧版本）已保存到: {output_dir / 'xiaozeng_receive_addresses_old.txt'}")

if __name__ == "__main__":
    import pandas as pd
    exit_code = main()
    sys.exit(exit_code)
