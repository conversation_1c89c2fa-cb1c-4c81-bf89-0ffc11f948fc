#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试时间数据格式
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "modules"))

from modules.data_reader import ExcelDataReader
import pandas as pd

def debug_time_data():
    """调试时间数据"""
    reader = ExcelDataReader(".")
    
    print("=" * 60)
    print("调试时间数据格式")
    print("=" * 60)
    
    # 读取小江币安充值记录
    print("\n1. 小江币安充值记录时间数据:")
    xj_deposits = reader.read_xiaojiang_binance_deposits()
    print(f"数据形状: {xj_deposits.shape}")
    print(f"列名: {list(xj_deposits.columns)}")
    print(f"时间列数据类型: {xj_deposits['时间'].dtype}")
    print("前5行时间数据:")
    for i in range(min(5, len(xj_deposits))):
        time_val = xj_deposits.iloc[i]['时间']
        print(f"  行{i}: {time_val} (类型: {type(time_val)})")
    
    # 读取小曾币安充值记录
    print("\n2. 小曾币安充值记录时间数据:")
    xz_deposits = reader.read_xiaozeng_binance_deposits()
    print(f"数据形状: {xz_deposits.shape}")
    print(f"列名: {list(xz_deposits.columns)}")
    print(f"时间列数据类型: {xz_deposits['时间'].dtype}")
    print("前5行时间数据:")
    for i in range(min(5, len(xz_deposits))):
        time_val = xz_deposits.iloc[i]['时间']
        print(f"  行{i}: {time_val} (类型: {type(time_val)})")
    
    # 检查有效时间数据
    print("\n3. 有效时间数据统计:")
    xj_valid_times = []
    for t in xj_deposits['时间']:
        if pd.notna(t) and isinstance(t, (int, float)):
            xj_valid_times.append(t)
    
    xz_valid_times = []
    for t in xz_deposits['时间']:
        if pd.notna(t) and isinstance(t, (int, float)):
            xz_valid_times.append(t)
    
    print(f"小江有效时间数据: {len(xj_valid_times)} / {len(xj_deposits)}")
    print(f"小曾有效时间数据: {len(xz_valid_times)} / {len(xz_deposits)}")
    
    if xj_valid_times:
        print(f"小江时间范围: {min(xj_valid_times)} - {max(xj_valid_times)}")
    if xz_valid_times:
        print(f"小曾时间范围: {min(xz_valid_times)} - {max(xz_valid_times)}")

if __name__ == "__main__":
    debug_time_data()
