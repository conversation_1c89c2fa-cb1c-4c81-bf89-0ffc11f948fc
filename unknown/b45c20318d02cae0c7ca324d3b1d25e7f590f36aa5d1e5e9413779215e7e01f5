#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单地址测试器
专门用于测试单个地址的详细信息和交易情况
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from datetime import datetime, timedelta
import json

class SingleAddressTester:
    """单地址测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.validator = AddressValidator()
        self.reader = ExcelDataReader(".")
        self.collector = AddressCollector(self.reader)
    
    def test_address_complete(self, address: str, query_transactions: bool = True) -> dict:
        """
        完整测试单个地址
        
        Args:
            address: 要测试的地址
            query_transactions: 是否查询交易记录
            
        Returns:
            完整的测试结果
        """
        print(f"\n{'='*80}")
        print(f"完整测试地址: {address}")
        print(f"{'='*80}")
        
        result = {
            'address': address,
            'test_time': datetime.now(),
            'validation': None,
            'transactions': None,
            'summary': {}
        }
        
        # 1. 地址验证
        print("🔍 步骤1: 地址验证")
        print("-" * 40)
        
        validation = self.validator.validate_address(address)
        result['validation'] = validation
        
        print(f"地址: {address}")
        print(f"类型: {validation['address_type']}")
        print(f"格式有效: {'✅ 是' if validation['format_valid'] else '❌ 否'}")
        print(f"链上存在: {'✅ 是' if validation['exists_on_chain'] else '❌ 否'}")
        print(f"应忽略: {'⚠️ 是' if validation['should_ignore'] else '✅ 否'}")
        
        if validation['chain_info']:
            print(f"链上信息:")
            for key, value in validation['chain_info'].items():
                print(f"  {key}: {value}")
        
        if validation['errors']:
            print(f"❌ 错误信息:")
            for error in validation['errors']:
                print(f"  - {error}")
        
        # 2. 交易查询（如果地址有效）
        if query_transactions and validation['format_valid'] and not validation['should_ignore']:
            print(f"\n🔍 步骤2: 交易查询")
            print("-" * 40)
            
            transactions = self._query_transactions(address, validation['address_type'])
            result['transactions'] = transactions
            
            if transactions:
                print(f"✅ 找到 {len(transactions)} 条交易记录")
                self._display_transactions(transactions)
            else:
                print("❌ 未找到交易记录")
        else:
            print(f"\n⏭️ 跳过交易查询（地址无效或应忽略）")
        
        # 3. 生成总结
        result['summary'] = self._generate_summary(validation, result.get('transactions'))
        
        print(f"\n📊 测试总结")
        print("-" * 40)
        for key, value in result['summary'].items():
            print(f"{key}: {value}")
        
        return result
    
    def _query_transactions(self, address: str, address_type: str) -> list:
        """查询交易记录"""
        transactions = []
        
        try:
            if address_type == 'TRC20':
                print("查询TRC20交易...")
                transactions = self.collector.query_trc20_transaction(address, None)
            elif address_type == 'ERC20':
                print("查询ERC20交易...")
                transactions = self.collector.query_erc20_transaction(address, None)
            else:
                print(f"不支持的地址类型: {address_type}")
        except Exception as e:
            print(f"❌ 交易查询失败: {e}")
        
        return transactions
    
    def _display_transactions(self, transactions: list, max_display: int = 5):
        """显示交易记录"""
        print(f"\n📋 交易记录详情（显示前{min(len(transactions), max_display)}条）:")
        
        for i, tx in enumerate(transactions[:max_display], 1):
            print(f"\n  交易 {i}:")
            print(f"    哈希: {tx.get('tx_hash', 'N/A')}")
            print(f"    发送方: {tx.get('from_address', 'N/A')}")
            print(f"    接收方: {tx.get('to_address', 'N/A')}")
            print(f"    金额: {tx.get('amount', 'N/A')}")
            print(f"    时间: {tx.get('timestamp', 'N/A')}")
            print(f"    区块: {tx.get('block', 'N/A')}")
        
        if len(transactions) > max_display:
            print(f"\n  ... 还有 {len(transactions) - max_display} 条交易")
    
    def _generate_summary(self, validation: dict, transactions: list) -> dict:
        """生成测试总结"""
        summary = {
            '地址状态': '未知',
            '地址类型': validation['address_type'],
            '验证结果': '失败',
            '交易数量': 0,
            '建议操作': '无'
        }
        
        # 确定地址状态
        if validation['should_ignore']:
            summary['地址状态'] = '应忽略'
            summary['建议操作'] = '忽略此地址'
        elif not validation['format_valid']:
            summary['地址状态'] = '格式无效'
            summary['建议操作'] = '检查地址格式'
        elif not validation['exists_on_chain']:
            summary['地址状态'] = '链上不存在'
            summary['建议操作'] = '可能是PDF扫描错误'
        else:
            summary['地址状态'] = '有效'
            summary['验证结果'] = '成功'
            summary['建议操作'] = '可用于进一步分析'
        
        # 交易信息
        if transactions:
            summary['交易数量'] = len(transactions)
            if len(transactions) > 0:
                summary['建议操作'] = '分析交易记录'
        
        return summary
    
    def test_address_at_time(self, address: str, timestamp: datetime, 
                           time_window_hours: int = 24) -> dict:
        """
        测试地址在特定时间的交易情况
        
        Args:
            address: 地址
            timestamp: 查询时间点
            time_window_hours: 时间窗口（小时）
            
        Returns:
            测试结果
        """
        print(f"\n{'='*80}")
        print(f"测试地址在特定时间的交易: {address}")
        print(f"查询时间: {timestamp}")
        print(f"时间窗口: ±{time_window_hours} 小时")
        print(f"{'='*80}")
        
        # 首先验证地址
        validation = self.validator.validate_address(address)
        
        if not validation['format_valid'] or validation['should_ignore']:
            print(f"❌ 地址无效或应忽略")
            return {'error': '地址无效', 'validation': validation}
        
        # 查询交易
        transactions = []
        if validation['address_type'] == 'TRC20':
            transactions = self.collector.query_trc20_transaction(address, timestamp)
        elif validation['address_type'] == 'ERC20':
            transactions = self.collector.query_erc20_transaction(address, timestamp)
        
        # 过滤时间窗口内的交易
        filtered_transactions = []
        if transactions:
            for tx in transactions:
                tx_time = tx.get('timestamp')
                if isinstance(tx_time, datetime):
                    time_diff = abs((tx_time - timestamp).total_seconds() / 3600)
                    if time_diff <= time_window_hours:
                        filtered_transactions.append({
                            **tx,
                            'time_diff_hours': time_diff
                        })
        
        print(f"📋 查询结果:")
        print(f"  总交易数: {len(transactions)}")
        print(f"  时间窗口内交易: {len(filtered_transactions)}")
        
        if filtered_transactions:
            print(f"\n⏰ 时间窗口内的交易:")
            for i, tx in enumerate(filtered_transactions, 1):
                print(f"  交易 {i}:")
                print(f"    时间: {tx.get('timestamp')}")
                print(f"    时间差: {tx.get('time_diff_hours', 0):.1f} 小时")
                print(f"    哈希: {tx.get('tx_hash', 'N/A')[:20]}...")
                print(f"    发送→接收: {tx.get('from_address', 'N/A')[:10]}...→{tx.get('to_address', 'N/A')[:10]}...")
        
        return {
            'address': address,
            'query_timestamp': timestamp,
            'time_window_hours': time_window_hours,
            'validation': validation,
            'total_transactions': len(transactions),
            'filtered_transactions': filtered_transactions
        }

def main():
    """主函数 - 在这里添加您要测试的地址"""
    tester = SingleAddressTester()
    
    # 在这里修改要测试的地址
    test_addresses = [
        "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew",  # 小江TRC20地址
        "0x61f73c531c47d345ee642566d9d9fb38db287cdb",  # 小江ERC20地址
        # 添加您要测试的其他地址...
    ]
    
    print("MoneyTrace 单地址测试器")
    print("=" * 80)
    
    for address in test_addresses:
        # 完整测试地址
        result = tester.test_address_complete(address, query_transactions=True)
        
        # 可选：测试特定时间的交易
        # test_time = datetime(2024, 1, 1, 12, 0, 0)
        # tester.test_address_at_time(address, test_time, 24)
        
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()
