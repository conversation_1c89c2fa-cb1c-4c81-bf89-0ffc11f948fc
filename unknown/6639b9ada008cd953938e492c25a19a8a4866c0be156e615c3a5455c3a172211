#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速地址测试脚本
简单快速地测试几个地址
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from modules.address_validator import AddressValidator
from modules.address_collector import AddressCollector
from modules.data_reader import ExcelDataReader
from datetime import datetime

def quick_test():
    """快速测试函数"""
    print("MoneyTrace 快速地址测试")
    print("=" * 50)
    
    # 初始化工具
    validator = AddressValidator()
    reader = ExcelDataReader(".")
    collector = AddressCollector(reader)
    
    # 测试地址列表 - 您可以在这里修改要测试的地址
    test_addresses = [
        # 来自readme.md的已知地址
        "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew",  # 小江TRC20地址
        "TUmBkCNdT9YzTpKqwAZwL5s92iTv6Nw8rh",   # 小江TRC20地址
        "0x61f73c531c47d345ee642566d9d9fb38db287cdb",  # 小江ERC20地址
        
        # 来自数据文件的地址样本
        "TXVL9P8ZiiABWq6D66HndrtVC2bQegve9q",
        "TM1zzNDZD2DPASbKcgdVoTYhfmYgtfwx9R",
        "0x91a0e36746161b20a7f8d0fba1fac0b97900e6bd",
        
        # UID测试
        "135343225",  # 小江UID（应忽略）
        "96215380",   # 小曾UID（应保留）
        
        # 无效地址测试
        "invalid_test_address",
    ]
    
    print(f"测试 {len(test_addresses)} 个地址...\n")
    
    for i, address in enumerate(test_addresses, 1):
        print(f"[{i}/{len(test_addresses)}] 测试地址: {address}")
        print("-" * 40)
        
        # 验证地址
        result = validator.validate_address(address)
        
        print(f"  类型: {result['address_type']}")
        print(f"  格式有效: {'✅' if result['format_valid'] else '❌'}")
        print(f"  链上存在: {'✅' if result['exists_on_chain'] else '❌'}")
        print(f"  应忽略: {'是' if result['should_ignore'] else '否'}")
        
        if result['errors']:
            print(f"  错误: {'; '.join(result['errors'])}")
        
        # 如果地址有效且不应忽略，查询交易
        if result['format_valid'] and not result['should_ignore'] and result['exists_on_chain']:
            print(f"  🔍 查询交易...")
            
            if result['address_type'] == 'TRC20':
                transactions = collector.query_trc20_transaction(address, None)
            elif result['address_type'] == 'ERC20':
                transactions = collector.query_erc20_transaction(address, None)
            else:
                transactions = []
            
            print(f"  📋 找到交易: {len(transactions)} 条")
            
            if transactions:
                # 显示最近的几条交易
                for j, tx in enumerate(transactions[:3], 1):
                    print(f"    交易{j}: {tx.get('tx_hash', 'N/A')[:20]}...")
                    print(f"      发送→接收: {tx.get('from_address', 'N/A')[:10]}...→{tx.get('to_address', 'N/A')[:10]}...")
                    print(f"      时间: {tx.get('timestamp', 'N/A')}")
        
        print()
    
    print("=" * 50)
    print("测试完成！")

def test_specific_addresses():
    """测试特定地址 - 您可以在这里添加要测试的具体地址"""
    
    # 在这里添加您要测试的具体地址
    specific_addresses = [
        # 示例：添加您想测试的地址
        # "TRFkzRWd8tXynZHgen3H7qbpuRSnM2uWew",
        # "0x61f73c531c47d345ee642566d9d9fb38db287cdb",
    ]
    
    if not specific_addresses:
        print("请在 test_specific_addresses() 函数中添加要测试的地址")
        return
    
    validator = AddressValidator()
    
    print("测试特定地址")
    print("=" * 30)
    
    for address in specific_addresses:
        print(f"\n测试: {address}")
        result = validator.validate_address(address)
        
        print(f"结果: {result['address_type']} | "
              f"格式{'✅' if result['format_valid'] else '❌'} | "
              f"存在{'✅' if result['exists_on_chain'] else '❌'}")
        
        if result['chain_info']:
            print(f"链上信息: {result['chain_info']}")

if __name__ == "__main__":
    # 运行快速测试
    quick_test()
    
    # 如果您想测试特定地址，取消下面的注释
    # test_specific_addresses()
