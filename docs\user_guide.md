# MoneyTrace 用户使用指南

## 项目概述

MoneyTrace 是一个资金流向追踪分析系统，专门用于分析加密货币交易记录，识别可疑资金流向。

## 功能模块

### 1. 简易分析模块 (已实现)

**功能描述**: 计算小江和小曾币安充值记录时间交集期间，小江共充值USDT减去小江P2P卖出(对应银行卡和微信收款)，得出小江实际USDT余额（可能流向小曾的嫌疑金额）。

**分析结果**:
- 分析时间段: 2021-11-09 到 2025-03-11 (1217天)
- 小江充值USDT总额: **1,387,476.25 USDT**
- 小江P2P卖出USDT总额: 650,055.45 USDT
- 可排除USDT金额: **556,365.13 USDT** (有对应法币收款证据)
- **嫌疑流向小曾的USDT: 831,111.12 USDT**
- 排除率: 40.10%

**证据链**: 系统找到176条完整的证据链，每条证据链包含：
- P2P卖出记录
- 对应的银行或微信收款记录
- 时间和金额匹配验证

### 2. 小曾收款地址获取模块 (已实现)

**功能描述**: 通过查询小曾币安充值记录中的发送地址，获取小曾所有收款地址的并集。

**分析结果**:
- 发现收款地址: **68个**
- 包含ERC20和TRC20地址格式
- 地址列表已保存到输出文件

## 使用方法

### 环境要求

- Python 3.8+
- 必要依赖包（见requirements.txt）

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行分析

```bash
python src/main.py
```

### 输出文件

分析完成后，结果将保存到 `data/output/` 目录：

1. **simple_analysis_report.txt**: 详细的简易分析报告
2. **xiaozeng_receive_addresses.txt**: 小曾收款地址列表

## 数据文件说明

### 输入文件

项目需要以下Excel文件作为输入数据：

| 文件名 | 描述 | 数据量 |
|--------|------|--------|
| xjbain.xls | 小江币安充值记录 | 510行 |
| xzbain.xls | 小曾币安充值记录 | 87行 |
| xjbasale.xls | 小江P2P卖出记录 | 222行 |
| xzbasale.xls | 小曾P2P卖出记录 | 70行 |
| xjbankinout.xls | 小江银行出入金记录 | 287行 |
| xzbankin.xls | 小江银行流水记录 | 40行 |
| xjwxin.xls | 小江微信收款记录 | 25行 |
| xzwxin.xls | 小曾微信收款记录 | 15行 |

### 数据格式

所有Excel文件都是.xls格式，包含以下关键字段：
- **时间字段**: 用于时间交集计算
- **金额字段**: USDT数额和法币金额
- **地址字段**: 区块链地址信息
- **支付方式**: BANK、WECHAT、ALIPAY等

## 分析逻辑

### 时间交集算法

1. 提取小江和小曾币安充值记录的时间范围
2. 计算两个时间范围的交集
3. 在交集期间内进行资金流向分析

### 资金排除算法

1. 统计小江在交集期间的USDT充值总额
2. 查找小江在同期的P2P卖出记录
3. 匹配P2P卖出与法币收款记录（48小时时间窗口）
4. 排除有法币收款证据的USDT金额
5. 剩余金额即为嫌疑流向小曾的资金

### 地址提取算法

1. 读取小曾币安充值记录
2. 提取所有发送地址字段
3. 去重并排序，生成收款地址列表

## 技术特点

### 数据处理

- **Excel文件读取**: 使用xlrd处理老格式.xls文件
- **时间处理**: 自动识别Excel日期格式并转换
- **数据验证**: 过滤无效数据，确保分析准确性

### 匹配算法

- **时间窗口匹配**: 48小时内的交易关联
- **金额容差**: 5%的金额误差容忍
- **多支付方式**: 支持银行、微信、支付宝等

### 证据链构建

- **完整性**: 每个排除项都有完整的证据链
- **可追溯**: 所有匹配过程都有详细记录
- **可验证**: 提供原始数据索引便于核查

## 结果解读

### 关键指标

1. **嫌疑金额**: 831,111.12 USDT
   - 这是小江充值但无法通过法币收款记录排除的USDT金额
   - 代表可能流向小曾的最大嫌疑金额

2. **排除率**: 40.10%
   - 表示40.10%的充值USDT有明确的法币收款证据
   - 剩余59.90%的资金流向存疑

3. **证据链数量**: 176条
   - 每条证据链代表一个完整的P2P卖出→法币收款流程
   - 证明了相应USDT的合法性

### 风险评估

- **高风险**: 嫌疑金额超过80万USDT，金额巨大
- **中等可信度**: 排除率40%，仍有大量资金流向不明
- **需要进一步调查**: 建议对收款地址进行链上追踪

## 后续建议

1. **地址验证**: 对68个收款地址进行区块链验证
2. **链上追踪**: 追踪嫌疑资金的具体流向路径
3. **时间细化**: 缩小时间窗口，提高匹配精度
4. **交叉验证**: 结合其他数据源进行交叉验证

## 技术支持

如需技术支持或功能扩展，请参考：
- 技术设计文档: `docs/technical_design.md`
- API参考文档: `docs/api_reference.md`
- 源代码: `src/` 目录
