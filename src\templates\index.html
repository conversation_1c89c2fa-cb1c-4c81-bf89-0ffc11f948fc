<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoneyTrace 地址测试工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .input-group { margin-bottom: 15px; }
        .input-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .input-group input, .input-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn:hover { opacity: 0.9; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; background: #ecf0f1; border: none; cursor: pointer; margin-right: 5px; }
        .tab.active { background: #3498db; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .transaction-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .transaction-table th, .transaction-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        .transaction-table th { background: #f8f9fa; }
        .loading { text-align: center; padding: 20px; }
        .status-badge { padding: 3px 8px; border-radius: 3px; font-size: 12px; }
        .status-valid { background: #d4edda; color: #155724; }
        .status-invalid { background: #f8d7da; color: #721c24; }
        .status-ignore { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 MoneyTrace 地址测试工具</h1>
            <p>测试区块链地址有效性和查询交易记录</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('single')">单地址测试</button>
            <button class="tab" onclick="showTab('batch')">批量测试</button>
            <button class="tab" onclick="showTab('transactions')">交易查询</button>
        </div>

        <!-- 单地址测试 -->
        <div id="single" class="tab-content active">
            <div class="card">
                <h3>单地址测试</h3>
                <div class="input-group">
                    <label for="singleAddress">输入地址:</label>
                    <input type="text" id="singleAddress" placeholder="输入TRC20或ERC20地址">
                </div>
                <button class="btn btn-primary" onclick="validateSingle()">验证地址</button>
                <div id="singleResult"></div>
            </div>
        </div>

        <!-- 批量测试 -->
        <div id="batch" class="tab-content">
            <div class="card">
                <h3>批量地址测试</h3>
                <div class="input-group">
                    <label for="batchAddresses">输入地址列表（每行一个）:</label>
                    <textarea id="batchAddresses" rows="10" placeholder="每行输入一个地址"></textarea>
                </div>
                <button class="btn btn-success" onclick="validateBatch()">批量验证</button>
                <div id="batchResult"></div>
            </div>
        </div>

        <!-- 交易查询 -->
        <div id="transactions" class="tab-content">
            <div class="card">
                <h3>交易记录查询</h3>
                <div class="input-group">
                    <label for="txAddress">输入地址:</label>
                    <input type="text" id="txAddress" placeholder="输入要查询交易的地址">
                </div>
                <button class="btn btn-warning" onclick="getTransactions()">查询交易</button>
                <div id="txResult"></div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        async function validateSingle() {
            const address = document.getElementById('singleAddress').value.trim();
            const resultDiv = document.getElementById('singleResult');
            
            if (!address) {
                resultDiv.innerHTML = '<div class="result error">请输入地址</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="loading">验证中...</div>';
            
            try {
                const response = await fetch('/api/validate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address: address })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    const statusClass = data.format_valid && data.exists_on_chain && !data.should_ignore ? 'success' : 
                                       data.should_ignore ? 'warning' : 'error';
                    
                    resultDiv.innerHTML = `
                        <div class="result ${statusClass}">
                            <h4>验证结果</h4>
                            <p><strong>地址:</strong> ${data.address}</p>
                            <p><strong>类型:</strong> ${data.address_type}</p>
                            <p><strong>格式有效:</strong> <span class="status-badge ${data.format_valid ? 'status-valid' : 'status-invalid'}">${data.format_valid ? '✅ 是' : '❌ 否'}</span></p>
                            <p><strong>链上存在:</strong> <span class="status-badge ${data.exists_on_chain ? 'status-valid' : 'status-invalid'}">${data.exists_on_chain ? '✅ 是' : '❌ 否'}</span></p>
                            <p><strong>应忽略:</strong> <span class="status-badge ${data.should_ignore ? 'status-ignore' : 'status-valid'}">${data.should_ignore ? '⚠️ 是' : '✅ 否'}</span></p>
                            ${data.errors.length > 0 ? '<p><strong>错误:</strong> ' + data.errors.join('; ') + '</p>' : ''}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">请求失败: ${error.message}</div>`;
            }
        }

        async function validateBatch() {
            const addresses = document.getElementById('batchAddresses').value.trim().split('\n').filter(addr => addr.trim());
            const resultDiv = document.getElementById('batchResult');
            
            if (addresses.length === 0) {
                resultDiv.innerHTML = '<div class="result error">请输入地址列表</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="loading">批量验证中...</div>';
            
            try {
                const response = await fetch('/api/batch_validate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ addresses: addresses })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>批量验证结果</h4>
                            <p><strong>总地址数:</strong> ${data.total_addresses}</p>
                            <p><strong>有效地址:</strong> ${data.valid_addresses.length}</p>
                            <p><strong>无效地址:</strong> ${data.invalid_addresses.length}</p>
                            <p><strong>忽略地址:</strong> ${data.ignored_addresses.length}</p>
                            <p><strong>TRC20地址:</strong> ${data.trc20_addresses.length}</p>
                            <p><strong>ERC20地址:</strong> ${data.erc20_addresses.length}</p>
                            
                            ${data.valid_addresses.length > 0 ? 
                                '<h5>✅ 有效地址:</h5><ul>' + data.valid_addresses.map(addr => '<li>' + addr + '</li>').join('') + '</ul>' : ''}
                            
                            ${data.invalid_addresses.length > 0 ? 
                                '<h5>❌ 无效地址:</h5><ul>' + data.invalid_addresses.slice(0, 10).map(addr => '<li>' + addr + '</li>').join('') + 
                                (data.invalid_addresses.length > 10 ? '<li>... 还有 ' + (data.invalid_addresses.length - 10) + ' 个</li>' : '') + '</ul>' : ''}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">请求失败: ${error.message}</div>`;
            }
        }

        async function getTransactions() {
            const address = document.getElementById('txAddress').value.trim();
            const resultDiv = document.getElementById('txResult');
            
            if (!address) {
                resultDiv.innerHTML = '<div class="result error">请输入地址</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="loading">查询交易中...</div>';
            
            try {
                const response = await fetch('/api/transactions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address: address })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    let html = `
                        <div class="result success">
                            <h4>交易查询结果</h4>
                            <p><strong>地址:</strong> ${data.address}</p>
                            <p><strong>交易数量:</strong> ${data.transaction_count}</p>
                    `;
                    
                    if (data.transactions.length > 0) {
                        html += `
                            <table class="transaction-table">
                                <thead>
                                    <tr>
                                        <th>交易哈希</th>
                                        <th>发送方</th>
                                        <th>接收方</th>
                                        <th>金额</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.transactions.forEach(tx => {
                            html += `
                                <tr>
                                    <td>${tx.hash.substring(0, 20)}...</td>
                                    <td>${tx.from.substring(0, 15)}...</td>
                                    <td>${tx.to.substring(0, 15)}...</td>
                                    <td>${tx.amount}</td>
                                    <td>${tx.timestamp}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                    } else {
                        html += '<p>未找到交易记录</p>';
                    }
                    
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="result error">错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">请求失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
